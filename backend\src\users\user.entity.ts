import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, ManyToOne } from 'typeorm';
import { UserRole, UserStatus } from '../shared/enums';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: string;

  @Column({ type: 'varchar', unique: true })
  email: string;

  @Column({ type: 'varchar', nullable: true })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  image: string;

  @Column({ type: 'enum', enum: UserRole, default: UserRole.STAFF })
  role: UserRole;

  @Column({ type: 'enum', enum: UserStatus, default: UserStatus.ACTIVE })
  status: UserStatus;

  @Column({ type: 'varchar', nullable: true })
  phone: string;

  @Column({ type: 'text', nullable: true })
  address: string;

  @Column({ type: 'varchar', nullable: true })
  city: string;

  @Column({ type: 'varchar', nullable: true })
  state: string;

  @Column({ type: 'varchar', nullable: true })
  zipCode: string;

  @Column({ type: 'varchar', nullable: true })
  country: string;

  @Column({ type: 'timestamp', nullable: true })
  lastLoginAt: Date;

  @Column({ type: 'boolean', default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'bigint', nullable: true })
  createdBy: string;

  @Column({ type: 'bigint', nullable: true })
  updatedBy: string;

  // Relations - using string literals to avoid circular dependency
  @OneToMany('Store', (store: any) => store.owner)
  ownedStores: any[];

  @OneToMany('Store', (store: any) => store.manager)
  managedStores: any[];

  @OneToMany('Order', (order: any) => order.user)
  orders: any[];

  @OneToMany('Order', (order: any) => order.createdByUser)
  createdOrders: any[];

  @OneToMany('Order', (order: any) => order.updatedByUser)
  updatedOrders: any[];

  @OneToMany('OrderItem', (orderItem: any) => orderItem.createdByUser)
  createdOrderItems: any[];

  @OneToMany('OrderItem', (orderItem: any) => orderItem.updatedByUser)
  updatedOrderItems: any[];

  @OneToMany('Product', (product: any) => product.createdByUser)
  createdProducts: any[];

  @OneToMany('Product', (product: any) => product.updatedByUser)
  updatedProducts: any[];

  @OneToMany('Customer', (customer: any) => customer.createdByUser)
  createdCustomers: any[];

  @OneToMany('Customer', (customer: any) => customer.updatedByUser)
  updatedCustomers: any[];

  @OneToMany('Store', (store: any) => store.createdByUser)
  createdStores: any[];

  @OneToMany('Store', (store: any) => store.updatedByUser)
  updatedStores: any[];

  @OneToMany('Conversation', (conversation: any) => conversation.user)
  conversations: any[];

  @OneToMany('Message', (message: any) => message.user)
  messages: any[];

  @OneToMany('Agent', (agent: any) => agent.createdByUser)
  createdAgents: any[];

  @OneToMany('Agent', (agent: any) => agent.updatedByUser)
  updatedAgents: any[];

  @OneToMany('ToolCall', (toolCall: any) => toolCall.user)
  toolCalls: any[];

  @OneToMany('ToolCall', (toolCall: any) => toolCall.createdByUser)
  createdToolCalls: any[];

  @OneToMany('ToolCall', (toolCall: any) => toolCall.updatedByUser)
  updatedToolCalls: any[];

  @OneToMany('Post', (post: any) => post.author)
  posts: any[];

  @OneToMany('Post', (post: any) => post.createdByUser)
  createdPosts: any[];

  @OneToMany('Post', (post: any) => post.updatedByUser)
  updatedPosts: any[];

  @OneToMany('Account', (account: any) => account.user)
  accounts: any[];

  @OneToMany('Session', (session: any) => session.user)
  sessions: any[];

  @OneToMany('VerificationToken', (verificationToken: any) => verificationToken.user)
  verificationTokens: any[];
}
