import { Repository } from 'typeorm';
import { ToolCall } from './tool-call.entity';
export declare class ToolCallsService {
    private toolCallsRepository;
    constructor(toolCallsRepository: Repository<ToolCall>);
    findAll(): Promise<ToolCall[]>;
    findOne(id: string): Promise<ToolCall>;
    findByConversationId(conversationId: string): Promise<ToolCall[]>;
    create(createToolCallDto: Partial<ToolCall>): Promise<ToolCall>;
    update(id: string, updateToolCallDto: Partial<ToolCall>): Promise<ToolCall>;
    remove(id: string): Promise<void>;
}
