import { useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../context/AuthContext';
import { useStore } from '../context/StoreContext';

// Routes that don't require authentication
const PUBLIC_ROUTES = ['/login', '/auth/callback', '/setup/store'];

export function useAuthGuard() {
  const router = useRouter();
  const { user, isLoading, error } = useAuth();
  const { currentStoreId, setCurrentStoreId } = useStore();
  const [storesData, setStoresData] = useState<any>(null);
  const [isStoresLoading, setIsStoresLoading] = useState(false);
  const handledCache = useRef<Record<string, boolean>>({});
  const isRedirectingRef = useRef(false);

  const isPublic = (path: string) => PUBLIC_ROUTES.some(route => path.startsWith(route));

  // Fetch stores data for the current user
  useEffect(() => {
    if (!user) return;

    const fetchStores = async () => {
      setIsStoresLoading(true);
      try {
        // Import the store API dynamically to avoid circular dependencies
        const { storeApi } = await import('../utils/api');
        const result = await storeApi.getByUserId(user.id, { page: 1, limit: 100 });
        setStoresData(result);
      } catch (error) {
        console.error('Error fetching stores:', error);
        setStoresData({ data: [], meta: { total: 0 } });
      } finally {
        setIsStoresLoading(false);
      }
    };

    fetchStores();
  }, [user]);

  // Main auth guard logic
  useEffect(() => {
    // Add a small delay to allow auth state to stabilize after token storage
    const timeoutId = setTimeout(() => {
      // Avoid redirecting while we are still determining auth state
      if (isLoading) return;

      const currentPath = router.pathname;
      const asPath = router.asPath;
      const fullPath = asPath || currentPath;
      const searchParams = new URLSearchParams(typeof window !== 'undefined' ? window.location.search : '');
      const loggedOutFlag = searchParams.get('loggedOut');

      // Ensure we only handle/log once per path after loading has completed
      if (handledCache.current[currentPath]) {
        return;
      }

      console.log('[AuthGuard] route/useEffect fired', {
        pathname: currentPath,
        asPath: router.asPath,
        isLoading,
        hasUser: !!user,
        error,
      });

      if (!user && !isPublic(currentPath)) {
        if (isRedirectingRef.current) {
          console.log('[AuthGuard] Redirect already in progress; skipping');
          return;
        }

        isRedirectingRef.current = true;
        const nextParam = encodeURIComponent(fullPath || '/');
        const loginUrl = `/login?next=${nextParam}`;
        console.log('[AuthGuard] Not authenticated; redirecting to', loginUrl);
        router.replace(loginUrl).finally(() => {
          setTimeout(() => { isRedirectingRef.current = false; }, 0);
        });
        handledCache.current[currentPath] = true;
        return;
      }

      if (isPublic(currentPath) && currentPath === '/login' && loggedOutFlag === '1') {
        handledCache.current[currentPath] = true;
        return;
      }

      if (user && isPublic(currentPath) && currentPath === '/login') {
        if (isStoresLoading) {
          return;
        }
        if (storesData === null) {
          return;
        }
        if (!isRedirectingRef.current) {
          isRedirectingRef.current = true;
          let target = '/dashboard';
          try {
            const nextParam = searchParams.get('next') || undefined;
            const totalActive = storesData?.meta?.total ?? storesData?.data?.length ?? 0;
            if (nextParam && nextParam.startsWith('/')) {
              target = nextParam;
            } else {
              if (totalActive === 0) {
                target = '/setup/store';
              }
            }
          } catch {}
          console.log('[AuthGuard] Already authenticated; redirecting away from public auth page to', target);
          router.replace(target).finally(() => {
            setTimeout(() => { isRedirectingRef.current = false; }, 0);
          });
        }
        handledCache.current[currentPath] = true;
        return;
      }

      if (user && !isPublic(currentPath)) {
        if (storesData === null) {
          console.log('[AuthGuard] Stores data not yet fetched, waiting...');
          return;
        }
        const totalActive = storesData?.meta?.total ?? storesData?.data?.length ?? 0;
        console.log('[AuthGuard] Store check:', {
          currentPath,
          totalActive,
          storesData,
          isStoresLoading,
          loggedOutFlag
        });
        if ((!isStoresLoading) && totalActive === 0 && currentPath !== '/setup/store' && loggedOutFlag !== '1') {
          if (!isRedirectingRef.current) {
            isRedirectingRef.current = true;
            console.log('[AuthGuard] Authenticated user without active stores; redirecting to /setup/store');
            router.replace('/setup/store').finally(() => {
              setTimeout(() => { isRedirectingRef.current = false; }, 0);
            });
            handledCache.current[currentPath] = true;
            return;
          }
        } else if (totalActive > 0 && !currentStoreId) {
          const firstId = storesData?.data?.[0]?.id;
          if (firstId) setCurrentStoreId(String(firstId));
        }
      }
      
      if (!(user && !isPublic(currentPath) && isStoresLoading)) {
        handledCache.current[currentPath] = true;
      }
    }, 100); // 100ms delay to allow auth state to stabilize

    return () => clearTimeout(timeoutId);
  }, [router.pathname, isLoading, user, error, isStoresLoading, storesData, currentStoreId, router, setCurrentStoreId]);

  return {
    user,
    isLoading,
    error,
    storesData,
    isStoresLoading,
    currentStoreId,
  };
}


