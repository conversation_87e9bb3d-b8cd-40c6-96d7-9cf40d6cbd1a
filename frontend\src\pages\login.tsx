import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useAuth } from '../context/AuthContext';
import { debugLogin } from '../utils/auth';
import { storeApi } from '../utils/api';

export default function Login() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { loginWithGoogle, user, isLoading: isAuthLoading, refresh } = useAuth() as any;
  const [devEmail, setDevEmail] = useState('<EMAIL>');
  const [devName, setDevName] = useState('Dev User');
  const [storesData, setStoresData] = useState<any>(null);
  const [isStoresLoading, setIsStoresLoading] = useState(false);
  
  const userIdBigInt = typeof user?.id !== 'undefined' ? (BigInt(user.id) as unknown as bigint) : (undefined as unknown as bigint);

  // Fetch stores data
  const fetchStores = async () => {
    if (!user || !userIdBigInt) {
      console.log('[Login] fetchStores: No user or userIdBigInt, skipping');
      return;
    }
    
    console.log('[Login] fetchStores: Starting fetch for user:', user.id);
    setIsStoresLoading(true);
    try {
      const result = await storeApi.getByUserId(userIdBigInt.toString(), { page: 1, limit: 100 });
      console.log('[Login] fetchStores: API response:', result);

      if (result && typeof result === 'object' && 'data' in result) {
        setStoresData(result);
        console.log('[Login] fetchStores: Set stores data with meta:', (result as any).meta);
      } else {
        const fallbackData = { data: Array.isArray(result) ? result : [], meta: { total: 0 } };
        setStoresData(fallbackData);
        console.log('[Login] fetchStores: Set fallback data:', fallbackData);
      }
    } catch (err) {
      console.error('[Login] fetchStores: Error fetching stores:', err);
      const errorData = { data: [], meta: { total: 0 } };
      setStoresData(errorData);
      console.log('[Login] fetchStores: Set error data:', errorData);
    } finally {
      setIsStoresLoading(false);
      console.log('[Login] fetchStores: Set loading to false');
    }
  };

  useEffect(() => {
    // Check for error in URL params
    if (router.query.error) {
      switch (router.query.error) {
        case 'auth_error':
          setError('Authentication failed. Please try again.');
          break;
        case 'auth_failed':
          setError('Login failed. Please check your credentials.');
          break;
        case 'token_error':
          setError('Token generation failed. Please try again.');
          break;
        default:
          setError('An error occurred during login.');
      }
    }
  }, [router.query.error]);

  useEffect(() => {
    // If explicitly landed here after logout, do not auto-redirect anywhere
    const loggedOut = typeof router.query.loggedOut === 'string' ? router.query.loggedOut : undefined;
    if (loggedOut === '1') return;

    if (!isAuthLoading && user) {
      // Wait for store lookup to finish before deciding where to go
      if (isStoresLoading) {
        console.log('[Login] Stores still loading, waiting...');
        return;
      }
      
      // Only proceed with redirect logic if we have actually fetched stores data
      if (storesData === null) {
        console.log('[Login] Stores data not yet fetched, waiting...');
        return;
      }
      
      const totalStores = storesData?.meta?.total ?? storesData?.data?.length ?? 0;
      
      console.log('[Login] Redirect decision:', {
        totalStores,
        storesData,
        isStoresLoading,
        user: user.id
      });
      
      // Only redirect to setup if user has no active stores
      if (totalStores === 0) {
        console.log('[Login] No stores found, redirecting to setup');
        router.replace('/setup/store');
        return;
      }
      // Otherwise honor next or go dashboard
      const nextParam = typeof router.query.next === 'string' ? router.query.next : undefined;
      const target = nextParam && nextParam.startsWith('/') ? nextParam : '/dashboard';
      console.log('[Login] Stores found, redirecting to:', target);
      router.replace(target, target);
    }
  }, [user, isAuthLoading, isStoresLoading, storesData, router.query.loggedOut, router]);

  // Fetch stores when user changes
  useEffect(() => {
    if (user) {
      fetchStores();
    }
  }, [user]);

  return (
    <>
      <Head>
        <title>Login - Teno Store Console</title>
        <meta name="description" content="Authenticate to the Teno Store console" />
      </Head>

      <div className="app-bg">
        <div className="glow-overlay" />
        <div className="grid-overlay" />

        <div className="relative max-w-md w-full space-y-8">
          <div className="text-center">
            <div className="icon-badge">
              <svg viewBox="0 0 24 24" className="h-7 w-7 text-emerald-400">
                <path fill="currentColor" d="M12 2a10 10 0 1 0 10 10A10.011 10.011 0 0 0 12 2Zm0 18a8 8 0 1 1 8-8a8.009 8.009 0 0 1-8 8Z" />
                <path fill="currentColor" d="M7 12a5 5 0 0 1 10 0h2a7 7 0 0 0-14 0Zm5-3a3 3 0 0 1 3 3h2a5 5 0 0 0-10 0h2a3 3 0 0 1 3-3Z" className="opacity-80" />
              </svg>
            </div>
            <h2 className="mt-6 text-3xl font-extrabold tracking-tight brand-gradient-text">
              Sign in to Teno Store
            </h2>
            <p className="mt-2 subtle-text">
              Authenticate to access your Teno workspace
            </p>
          </div>

          <div className="glass-card">
            <div className="card-accent" />

            {error && (
              <div className="relative mb-4 rounded-md border border-red-500/20 bg-red-500/10 px-4 py-3 text-red-200">
                {error}
              </div>
            )}

            <div className="space-y-6">
              <button
                onClick={() => {
                  setIsLoading(true);
                  setError(null);
                  loginWithGoogle();
                }}
                disabled={isLoading}
                className="group primary-button"
              >
                <span className="flex items-center">
                  <svg className="w-5 h-5" viewBox="0 0 24 24">
                    <path
                      fill="#4285F4"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="#34A853"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="#FBBC05"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="#EA4335"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                </span>
                <span>{isLoading ? 'Connecting...' : 'Continue with Google'}</span>
                <span className="button-glow group-hover:opacity-100" />
              </button>

              { (process.env.NEXT_PUBLIC_DEBUG_AUTH === '1' || process.env.NODE_ENV !== 'production') && (
                <div className="space-y-3">
                  <div className="grid grid-cols-1 gap-3">
                    <input
                      type="email"
                      value={devEmail}
                      onChange={(e) => setDevEmail(e.target.value)}
                      placeholder="<EMAIL>"
                      className="w-full rounded-md bg-slate-800/50 border border-slate-700 px-3 py-2 text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-emerald-500/60"
                    />
                    <input
                      type="text"
                      value={devName}
                      onChange={(e) => setDevName(e.target.value)}
                      placeholder="Optional name"
                      className="w-full rounded-md bg-slate-800/50 border border-slate-700 px-3 py-2 text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-emerald-500/60"
                    />
                  </div>
                  <button
                    onClick={async () => {
                      try {
                        setIsLoading(true);
                        setError(null);
                        const user = await debugLogin(devEmail, devName);
                        await refresh?.();
                        const nextParam = typeof router.query.next === 'string' ? router.query.next : undefined;
                        const target = nextParam && nextParam.startsWith('/') ? nextParam : '/dashboard';
                        router.replace(target, target);
                      } catch (e) {
                        setError('Debug login failed');
                      } finally {
                        setIsLoading(false);
                      }
                    }}
                    disabled={isLoading}
                    className="group secondary-button w-full"
                  >
                    <span>{isLoading ? 'Connecting...' : 'Debug login (no password)'}</span>
                  </button>
                </div>
              )}

              <div className="text-center">
                <span
                  className="subtle-text hover:text-slate-100 cursor-pointer"
                  onClick={() => window.location.href = '/'}
                >
                  Back to Home
                </span>
              </div>
            </div>
          </div>

          
        </div>
      </div>
    </>
  );
}
