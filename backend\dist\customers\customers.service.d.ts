import { Repository } from 'typeorm';
import { Customer } from './customer.entity';
export declare class CustomersService {
    private customersRepository;
    constructor(customersRepository: Repository<Customer>);
    findAll(): Promise<Customer[]>;
    findOne(id: string): Promise<Customer>;
    findByStoreId(storeId: string): Promise<Customer[]>;
    create(createCustomerDto: Partial<Customer>): Promise<Customer>;
    update(id: string, updateCustomerDto: Partial<Customer>): Promise<Customer>;
    remove(id: string): Promise<void>;
}
