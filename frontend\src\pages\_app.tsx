import type { AppProps } from 'next/app';
import '../styles/globals.css';
import '../components/EntityTable.fadein.css';
import { AuthProvider } from '../context/AuthContext';
import { StoreProvider } from '../context/StoreContext';
import { PreferencesProvider } from '../context/PreferencesContext';
import { useAuthGuard } from '../utils/useAuthGuard';

function MyApp({ Component, pageProps }: AppProps) {
  return (
    <AuthProvider>
      <StoreProvider>
        <PreferencesProvider>
          <GuardedApp>
            <Component {...pageProps} />
          </GuardedApp>
        </PreferencesProvider>
      </StoreProvider>
    </AuthProvider>
  );
}

export default MyApp;


function GuardedApp({ children }: { children: React.ReactNode }) {
  // Run global auth guard on every route navigation
  useAuthGuard({
    publicPaths: ['/', '/login', '/_error', '/setup/store', '/store/[storeUuid]', '/storefront', '/live/[uuid]'],
  });

  return <>{children}</>;
}

