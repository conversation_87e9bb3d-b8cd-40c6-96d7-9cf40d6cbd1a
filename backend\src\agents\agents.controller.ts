import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AgentsService } from './agents.service';
import { Agent } from './agent.entity';

@ApiTags('agents')
@Controller('agents')
export class AgentsController {
  constructor(private readonly agentsService: AgentsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new agent' })
  @ApiResponse({ status: 201, description: 'Agent created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  create(@Body() createAgentDto: Partial<Agent>) {
    return this.agentsService.create(createAgentDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all agents' })
  @ApiResponse({ status: 200, description: 'Agents retrieved successfully' })
  findAll() {
    return this.agentsService.findAll();
  }

  @Get('store/:storeId')
  @ApiOperation({ summary: 'Get agents by store ID' })
  @ApiResponse({ status: 200, description: 'Agents retrieved successfully' })
  findByStoreId(@Param('storeId') storeId: string) {
    return this.agentsService.findByStoreId(storeId);
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get agents by user ID' })
  @ApiResponse({ status: 200, description: 'Agents retrieved successfully' })
  findByUserId(@Param('userId') userId: string) {
    return this.agentsService.findByUserId(userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get an agent by ID' })
  @ApiResponse({ status: 200, description: 'Agent retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  findOne(@Param('id') id: string) {
    return this.agentsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an agent' })
  @ApiResponse({ status: 200, description: 'Agent updated successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  update(@Param('id') id: string, @Body() updateAgentDto: Partial<Agent>) {
    return this.agentsService.update(id, updateAgentDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete an agent' })
  @ApiResponse({ status: 200, description: 'Agent deleted successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  remove(@Param('id') id: string) {
    return this.agentsService.remove(id);
  }
}
