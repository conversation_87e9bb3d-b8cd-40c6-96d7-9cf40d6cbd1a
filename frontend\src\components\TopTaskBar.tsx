import { useEffect, useMemo, useState, useRef } from 'react';
import { useRouter } from 'next/router';
import { storeApi } from '../utils/api';
import { useAuth } from '../context/AuthContext';
import { useStore } from '../context/StoreContext';
import { usePreferences } from '../context/PreferencesContext';
import { CURRENCY_SYMBOLS, CURRENCY_OPTIONS, LANGUAGE_OPTIONS, SupportedCurrency, SupportedLanguage } from '../utils/preferences';

interface StoreFormState {
  name: string;
  description: string;
  currency: string;
  preferredLanguage: string;
  userId: string | number;
}

interface FormErrors {
  name?: string;
  description?: string;
  currency?: string;
  preferredLanguage?: string;
}

interface StoreAction {
  type: 'edit' | 'delete' | 'view';
  store: any;
}

// Currency names mapping for display labels
const CURRENCY_NAMES: Record<string, string> = {
  'USD': 'US Dollar',
  'EUR': 'Euro',
  'GBP': 'British Pound',
  'CAD': 'Canadian Dollar',
  'AUD': 'Australian Dollar',
  'NZD': 'New Zealand Dollar',
  'JPY': 'Japanese Yen',
  'CNY': 'Chinese Yuan',
  'HKD': 'Hong Kong Dollar',
  'SGD': 'Singapore Dollar',
  'INR': 'Indian Rupee',
  'BRL': 'Brazilian Real',
  'MXN': 'Mexican Peso',
  'ZAR': 'South African Rand',
  'SEK': 'Swedish Krona',
  'NOK': 'Norwegian Krone',
  'DKK': 'Danish Krone',
  'CHF': 'Swiss Franc',
  'PLN': 'Polish Zloty',
  'CZK': 'Czech Koruna',
  'HUF': 'Hungarian Forint',
  'ILS': 'Israeli Shekel',
  'TRY': 'Turkish Lira',
  'AED': 'UAE Dirham',
  'SAR': 'Saudi Riyal',
  'DZD': 'Algerian Dinar',
  'QAR': 'Qatari Riyal',
  'KWD': 'Kuwaiti Dinar',
  'BHD': 'Bahraini Dinar',
  'OMR': 'Omani Rial',
  'EGP': 'Egyptian Pound',
  'NGN': 'Nigerian Naira',
  'KES': 'Kenyan Shilling',
  'ARS': 'Argentine Peso',
  'CLP': 'Chilean Peso',
  'COP': 'Colombian Peso',
  'PEN': 'Peruvian Sol',
  'UYU': 'Uruguayan Peso',
  'KRW': 'South Korean Won',
  'THB': 'Thai Baht',
  'MYR': 'Malaysian Ringgit',
  'PHP': 'Philippine Peso',
  'IDR': 'Indonesian Rupiah',
};

// Generate currency options from centralized symbols
const currencyOptions = CURRENCY_OPTIONS.map(currency => ({
  value: currency,
  label: `${CURRENCY_NAMES[currency]} (${currency})`,
  symbol: CURRENCY_SYMBOLS[currency]
}));

// Language names mapping for display labels
const LANGUAGE_NAMES: Record<string, { label: string; nativeLabel: string }> = {
  'en': { label: 'English', nativeLabel: 'English' },
  'es': { label: 'Spanish', nativeLabel: 'Español' },
  'fr': { label: 'French', nativeLabel: 'Français' },
  'de': { label: 'German', nativeLabel: 'Deutsch' },
  'it': { label: 'Italian', nativeLabel: 'Italiano' },
  'pt': { label: 'Portuguese', nativeLabel: 'Português' },
  'ru': { label: 'Russian', nativeLabel: 'Русский' },
  'zh': { label: 'Chinese', nativeLabel: '中文' },
  'ja': { label: 'Japanese', nativeLabel: '日本語' },
  'ko': { label: 'Korean', nativeLabel: '한국어' },
  'ar': { label: 'Arabic', nativeLabel: 'العربية' },
  'hi': { label: 'Hindi', nativeLabel: 'हिन्दी' },
  'bn': { label: 'Bengali', nativeLabel: 'বাংলা' },
  'pa': { label: 'Punjabi', nativeLabel: 'ਪੰਜਾਬੀ' },
  'ur': { label: 'Urdu', nativeLabel: 'اردو' },
  'fa': { label: 'Persian', nativeLabel: 'فارسی' },
  'tr': { label: 'Turkish', nativeLabel: 'Türkçe' },
  'nl': { label: 'Dutch', nativeLabel: 'Nederlands' },
  'sv': { label: 'Swedish', nativeLabel: 'Svenska' },
  'no': { label: 'Norwegian', nativeLabel: 'Norsk' },
  'da': { label: 'Danish', nativeLabel: 'Dansk' },
  'fi': { label: 'Finnish', nativeLabel: 'Suomi' },
  'pl': { label: 'Polish', nativeLabel: 'Polski' },
  'cs': { label: 'Czech', nativeLabel: 'Čeština' },
  'sk': { label: 'Slovak', nativeLabel: 'Slovenčina' },
  'hu': { label: 'Hungarian', nativeLabel: 'Magyar' },
  'ro': { label: 'Romanian', nativeLabel: 'Română' },
  'bg': { label: 'Bulgarian', nativeLabel: 'Български' },
  'hr': { label: 'Croatian', nativeLabel: 'Hrvatski' },
  'sl': { label: 'Slovenian', nativeLabel: 'Slovenščina' },
};

// Generate language options from centralized options
const languageOptions = LANGUAGE_OPTIONS.map(lang => ({
  value: lang,
  label: LANGUAGE_NAMES[lang]?.label || lang,
  nativeLabel: LANGUAGE_NAMES[lang]?.nativeLabel || lang
}));

export default function TopTaskBar() {
  const router = useRouter();
  const { user, logout } = useAuth();
  const { currency: preferredCurrency, language: preferredLanguage, setCurrency, setLanguage } = usePreferences();
  const { currentStoreId, setCurrentStoreId, autoSelectFirstStore } = useStore();

  // State for stores
  const [stores, setStores] = useState<any[]>([]);
  const [storesLoading, setStoresLoading] = useState(false);

  // State for modals and menu
  const [isStoreModalOpen, setIsStoreModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [isStoreMenuOpen, setIsStoreMenuOpen] = useState(false);

  // State for forms
  const [storeForm, setStoreForm] = useState<StoreFormState>({
    name: '',
    description: '',
    currency: preferredCurrency,
    preferredLanguage: preferredLanguage,
    userId: user?.id || '',
  });

  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingStoreId, setEditingStoreId] = useState<string | null>(null);
  const [deleteStoreId, setDeleteStoreId] = useState<string | null>(null);

  // Refs for focus management
  const storeNameInputRef = useRef<HTMLInputElement>(null);
  const storeMenuRef = useRef<HTMLDivElement>(null);

  // Fetch stores when user is available
  useEffect(() => {
    if (user?.id) {
      fetchStores();
    }
  }, [user?.id]);

  // Focus management for modals
  useEffect(() => {
    if (isStoreModalOpen && storeNameInputRef.current) {
      storeNameInputRef.current.focus();
    }
  }, [isStoreModalOpen]);

  // Click outside to close store menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (storeMenuRef.current && !storeMenuRef.current.contains(event.target as Node)) {
        setIsStoreMenuOpen(false);
      }
    };

    if (isStoreMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isStoreMenuOpen]);

  // Escape key to close menu
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isStoreMenuOpen) {
        setIsStoreMenuOpen(false);
      }
    };

    if (isStoreMenuOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isStoreMenuOpen]);

  // Close menu after actions
  const handleCreateModalOpen = () => {
    openCreateModal();
    setIsStoreMenuOpen(false);
  };

  const handleEditModalOpen = (store: any) => {
    openEditModal(store);
    setIsStoreMenuOpen(false);
  };

  const handleDeleteModalOpen = (store: any) => {
    openDeleteModal(store);
    setIsStoreMenuOpen(false);
  };

  const fetchStores = async () => {
    if (!user?.id) return;
    
    setStoresLoading(true);
    try {
      const data = await storeApi.getByUserId(user.id, { page: 1, limit: 100 });
      const storesData = (data as any)?.data || [];
      setStores(storesData);

      // Auto-select the first store if none is currently selected
      autoSelectFirstStore(storesData);
    } catch (error) {
      console.error('Failed to fetch stores:', error);
    } finally {
      setStoresLoading(false);
    }
  };

  const validateForm = (): FormErrors => {
    const errors: FormErrors = {};
    
    if (!storeForm.name.trim()) {
      errors.name = 'Store name is required';
    } else if (storeForm.name.trim().length < 2) {
      errors.name = 'Store name must be at least 2 characters';
    } else if (storeForm.name.trim().length > 50) {
      errors.name = 'Store name must be less than 50 characters';
    }
    
    if (storeForm.description.trim().length > 200) {
      errors.description = 'Description must be less than 200 characters';
    }
    
    if (!storeForm.currency) {
      errors.currency = 'Currency is required';
    }
    
    if (!storeForm.preferredLanguage) {
      errors.preferredLanguage = 'Language is required';
    }
    
    return errors;
  };

  const resetForm = () => {
    setStoreForm({
      name: '',
      description: '',
      currency: preferredCurrency,
      preferredLanguage: preferredLanguage,
      userId: user?.id || '',
    });
    setFormErrors({});
    setEditingStoreId(null);
  };

  const openCreateModal = () => {
    resetForm();
    setModalMode('create');
    setIsStoreModalOpen(true);
  };

  const openEditModal = (store: any) => {
    setStoreForm({
      name: store.name,
      description: store.description || '',
      currency: store.currency,
      preferredLanguage: store.preferredLanguage,
      userId: user?.id || '',
    });
    setEditingStoreId(store.id);
    setModalMode('edit');
    setFormErrors({});
    setIsStoreModalOpen(true);
  };

  const handleSubmitStore = async () => {
    const errors = validateForm();
    setFormErrors(errors);
    
    if (Object.keys(errors).length > 0) {
      return;
    }
    
    setIsSubmitting(true);
    try {
      if (modalMode === 'create') {
        await storeApi.create({
          ...storeForm,
          userId: user?.id || ''
        });
      } else {
        await storeApi.update(editingStoreId!, storeForm);
      }
      
      setIsStoreModalOpen(false);
      resetForm();
      fetchStores();
    } catch (error) {
      console.error(`Failed to ${modalMode} store:`, error);
      // You might want to show a toast notification here
    } finally {
      setIsSubmitting(false);
    }
  };

  const openDeleteModal = (store: any) => {
    setDeleteStoreId(store.id);
    setIsDeleteOpen(true);
  };

  const handleDeleteStore = async () => {
    if (!deleteStoreId) return;
    
    setIsSubmitting(true);
    try {
      await storeApi.delete(deleteStoreId);
      setIsDeleteOpen(false);
      setDeleteStoreId(null);
      fetchStores();
      
      // If we deleted the current store, clear it
      if (currentStoreId === deleteStoreId) {
        setCurrentStoreId(null);
      }
    } catch (error) {
      console.error('Failed to delete store:', error);
    } finally {
      setIsSubmitting(false);
    }
  };



  const handleLogout = async () => {
    try {
      await logout();
      router.replace('/login?loggedOut=1');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const currentStore = stores.find(store => store.id === currentStoreId);

  return (
    <div className="bg-slate-900 shadow-sm border-b border-white/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Left side - Logo and Store Selector */}
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              <h1 className="text-xl font-bold text-slate-100">Teno Store</h1>
            </div>

            {/* Unified Store Selector & Management */}
            <div className="relative" ref={storeMenuRef}>
              <button
                onClick={() => setIsStoreMenuOpen(!isStoreMenuOpen)}
                className="inline-flex items-center px-4 py-2 border border-slate-600 text-sm leading-4 font-medium rounded-md text-slate-300 bg-slate-800 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-colors min-w-[220px]"
                disabled={storesLoading}
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                <span className="flex-1 text-left truncate">
                  {storesLoading ? 'Loading...' : (currentStore ? currentStore.name : 'Select Store')}
                </span>
                <svg className={`w-4 h-4 ml-2 transition-transform ${isStoreMenuOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {isStoreMenuOpen && (
                <div className="absolute left-0 mt-2 w-full min-w-[280px] bg-slate-800 border border-slate-600 rounded-lg shadow-xl z-50">
                  <div className="py-2">
                    {/* Create Store */}
                    <button
                      onClick={handleCreateModalOpen}
                      className="flex items-center px-4 py-3 text-sm text-emerald-400 hover:bg-slate-700 hover:text-emerald-300 w-full text-left transition-colors border-b border-slate-600"
                    >
                      <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      <div>
                        <div className="font-medium">Create New Store</div>
                        <div className="text-xs text-slate-400">Set up a new store</div>
                      </div>
                    </button>
                    
                    {/* Store List */}
                    {stores.length > 0 && (
                      <>
                        <div className="px-4 py-2 border-b border-slate-600">
                          <p className="text-xs text-slate-400 uppercase tracking-wide font-medium">
                            Select Store
                          </p>
                        </div>
                        <div className="max-h-60 overflow-y-auto">
                          {/* No Store Option */}
                          <button
                            onClick={() => {
                              setCurrentStoreId(null);
                              setIsStoreMenuOpen(false);
                            }}
                            className={`flex items-center px-4 py-2 text-sm w-full text-left transition-colors ${
                              !currentStoreId 
                                ? 'bg-slate-700 text-slate-100' 
                                : 'text-slate-300 hover:bg-slate-700 hover:text-slate-100'
                            }`}
                          >
                            <div className="w-2 h-2 rounded-full bg-slate-500 mr-3" />
                            <span className="italic">No store selected</span>
                            {!currentStoreId && (
                              <svg className="w-4 h-4 ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                            )}
                          </button>
                          
                          {/* Store Options */}
                          {stores.map((store) => (
                            <div key={store.id} className="relative group">
                              <button
                                onClick={() => {
                                  setCurrentStoreId(store.id);
                                  setIsStoreMenuOpen(false);
                                }}
                                className={`flex items-center px-4 py-2 text-sm w-full text-left transition-colors ${
                                  currentStoreId === store.id 
                                    ? 'bg-emerald-600/20 text-emerald-200' 
                                    : 'text-slate-300 hover:bg-slate-700 hover:text-slate-100'
                                }`}
                              >
                                <div className={`w-2 h-2 rounded-full mr-3 ${
                                  currentStoreId === store.id ? 'bg-emerald-400' : 'bg-slate-400'
                                }`} />
                                <div className="flex-1 min-w-0">
                                  <div className="font-medium truncate">{store.name}</div>
                                  {store.description && (
                                    <div className="text-xs text-slate-400 truncate">{store.description}</div>
                                  )}
                                </div>
                                {currentStoreId === store.id && (
                                  <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                  </svg>
                                )}
                              </button>
                              
                              {/* Store Actions - Show on hover or if current */}
                              {(currentStoreId === store.id) && (
                                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1">
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleEditModalOpen(store);
                                    }}
                                    className="p-1 rounded text-slate-400 hover:text-slate-200 hover:bg-slate-600 transition-colors"
                                    title="Edit store"
                                  >
                                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleDeleteModalOpen(store);
                                    }}
                                    className="p-1 rounded text-slate-400 hover:text-red-400 hover:bg-slate-600 transition-colors"
                                    title="Delete store"
                                  >
                                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                  </button>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </>
                    )}
                    
                    {/* Empty State */}
                    {stores.length === 0 && !storesLoading && (
                      <div className="px-4 py-6 text-center">
                        <svg className="w-8 h-8 mx-auto text-slate-500 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                        <p className="text-sm text-slate-400 mb-2">No stores found</p>
                        <p className="text-xs text-slate-500">Create your first store to get started</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right side - User Menu and Preferences */}
          <div className="flex items-center space-x-4">
            {/* Currency Selector */}
            <div className="relative">
              <select
                value={preferredCurrency}
                onChange={(e) => setCurrency(e.target.value as SupportedCurrency)}
                className="block w-full pl-3 pr-10 py-2 text-base border-slate-600 bg-slate-800 text-slate-100 focus:outline-none focus:ring-2 focus:ring-emerald-400/50 focus:ring-emerald-400/50 sm:text-sm rounded-md"
              >
                {currencyOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.symbol} {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Language Selector */}
            <div className="relative">
              <select
                value={preferredLanguage}
                onChange={(e) => setLanguage(e.target.value as SupportedLanguage)}
                className="block w-full pl-3 pr-10 py-2 text-base border-slate-600 bg-slate-800 text-slate-100 focus:outline-none focus:ring-2 focus:ring-emerald-400/50 focus:border-emerald-400/50 sm:text-sm rounded-md"
              >
                {languageOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.nativeLabel}
                  </option>
                ))}
              </select>
            </div>

            {/* User Menu */}
            <div className="relative">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-slate-300">{user?.name}</span>
                <button
                  onClick={handleLogout}
                  className="text-sm text-slate-400 hover:text-slate-200 transition-colors"
                >
                  Logout
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Store Modal (Create/Edit) */}
      {isStoreModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div className="absolute inset-0 bg-black/50" onClick={() => setIsStoreModalOpen(false)} />

          {/* Modal Shell */}
          <div className="relative w-full max-w-lg overflow-hidden rounded-2xl border border-white/10 shadow-2xl bg-gradient-to-br from-slate-800/95 to-slate-900/95">
            <div className="absolute -inset-0.5 rounded-2xl bg-gradient-to-r from-emerald-500/20 via-cyan-500/20 to-indigo-500/20 blur opacity-60" />

            {/* Content */}
            <div className="relative flex flex-col max-h-[90vh]">
              {/* Header */}
              <div className="px-6 py-4 border-b border-white/10 flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${modalMode === 'create' ? 'bg-emerald-400' : 'bg-blue-400'}`} />
                  <h3 className="text-lg font-semibold text-slate-100">
                    {modalMode === 'create' ? 'Create New Store' : 'Edit Store'}
                  </h3>
                </div>
                <button 
                  onClick={() => setIsStoreModalOpen(false)}
                  className="text-slate-400 hover:text-slate-200 rounded-md p-1 transition-colors"
                >
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Body */}
              <div className="p-6 overflow-y-auto">
                <div className="space-y-5">
                  {/* Store Name */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-slate-200">
                      Store Name <span className="text-red-400">*</span>
                    </label>
                    <input
                      ref={storeNameInputRef}
                      type="text"
                      value={storeForm.name}
                      onChange={(e) => {
                        setStoreForm({ ...storeForm, name: e.target.value });
                        if (formErrors.name) setFormErrors({ ...formErrors, name: undefined });
                      }}
                      className={`w-full px-4 py-3 bg-slate-800/60 border rounded-xl text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-2 transition-all ${
                        formErrors.name 
                          ? 'border-red-500/50 focus:ring-red-400/50 focus:border-red-400/50' 
                          : 'border-slate-600/50 focus:ring-emerald-400/50 focus:border-emerald-400/50'
                      }`}
                      placeholder="e.g., Tech Haven, Fashion Forward, Green Groceries"
                      maxLength={50}
                    />
                    {formErrors.name && (
                      <p className="text-red-400 text-xs flex items-center mt-1">
                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        {formErrors.name}
                      </p>
                    )}
                    <p className="text-xs text-slate-400">{storeForm.name.length}/50 characters</p>
                  </div>

                  {/* Description */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-slate-200">Description</label>
                    <textarea
                      value={storeForm.description}
                      onChange={(e) => {
                        setStoreForm({ ...storeForm, description: e.target.value });
                        if (formErrors.description) setFormErrors({ ...formErrors, description: undefined });
                      }}
                      className={`w-full px-4 py-3 bg-slate-800/60 border rounded-xl text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-2 transition-all resize-none ${
                        formErrors.description 
                          ? 'border-red-500/50 focus:ring-red-400/50 focus:border-red-400/50' 
                          : 'border-slate-600/50 focus:ring-emerald-400/50 focus:border-emerald-400/50'
                      }`}
                      placeholder="Describe your store's mission, products, or specialty..."
                      rows={3}
                      maxLength={200}
                    />
                    {formErrors.description && (
                      <p className="text-red-400 text-xs flex items-center mt-1">
                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        {formErrors.description}
                      </p>
                    )}
                    <p className="text-xs text-slate-400">{storeForm.description.length}/200 characters</p>
                  </div>

                  {/* Currency and Language Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Currency */}
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-slate-200">
                        Currency <span className="text-red-400">*</span>
                      </label>
                      <select
                        value={storeForm.currency}
                        onChange={(e) => {
                          setStoreForm({ ...storeForm, currency: e.target.value });
                          if (formErrors.currency) setFormErrors({ ...formErrors, currency: undefined });
                        }}
                        className={`w-full px-4 py-3 bg-slate-800/60 border rounded-xl text-slate-100 focus:outline-none focus:ring-2 transition-all ${
                          formErrors.currency 
                            ? 'border-red-500/50 focus:ring-red-400/50 focus:border-red-400/50' 
                            : 'border-slate-600/50 focus:ring-emerald-400/50 focus:border-emerald-400/50'
                        }`}
                      >
                        <option value="">Select currency</option>
                        {currencyOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.symbol} {option.label}
                          </option>
                        ))}
                      </select>
                      {formErrors.currency && (
                        <p className="text-red-400 text-xs flex items-center mt-1">
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          {formErrors.currency}
                        </p>
                      )}
                    </div>

                    {/* Language */}
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-slate-200">
                        Language <span className="text-red-400">*</span>
                      </label>
                      <select
                        value={storeForm.preferredLanguage}
                        onChange={(e) => {
                          setStoreForm({ ...storeForm, preferredLanguage: e.target.value });
                          if (formErrors.preferredLanguage) setFormErrors({ ...formErrors, preferredLanguage: undefined });
                        }}
                        className={`w-full px-4 py-3 bg-slate-800/60 border rounded-xl text-slate-100 focus:outline-none focus:ring-2 transition-all ${
                          formErrors.preferredLanguage 
                            ? 'border-red-500/50 focus:ring-red-400/50 focus:border-red-400/50' 
                            : 'border-slate-600/50 focus:ring-emerald-400/50 focus:border-emerald-400/50'
                        }`}
                      >
                        <option value="">Select language</option>
                        {languageOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.nativeLabel}
                          </option>
                        ))}
                      </select>
                      {formErrors.preferredLanguage && (
                        <p className="text-red-400 text-xs flex items-center mt-1">
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          {formErrors.preferredLanguage}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="px-6 py-4 border-t border-white/10 flex items-center justify-end gap-3">
                <button
                  onClick={() => setIsStoreModalOpen(false)}
                  disabled={isSubmitting}
                  className="border border-slate-600 text-slate-300 bg-slate-800/50 hover:bg-slate-700/50 px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSubmitStore}
                  disabled={isSubmitting || !storeForm.name.trim() || !storeForm.currency || !storeForm.preferredLanguage}
                  className="px-6 py-2 bg-gradient-to-r from-emerald-600 to-emerald-500 hover:from-emerald-500 hover:to-emerald-400 text-white rounded-lg font-medium shadow-lg shadow-emerald-500/25 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center"
                >
                  {isSubmitting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                      </svg>
                      {modalMode === 'create' ? 'Creating...' : 'Updating...'}
                    </>
                  ) : (
                    <>
                      {modalMode === 'create' ? (
                        <>
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                          </svg>
                          Create Store
                        </>
                      ) : (
                        <>
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          Update Store
                        </>
                      )}
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Store Modal */}
      {isDeleteOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div className="absolute inset-0 bg-black/50" onClick={() => setIsDeleteOpen(false)} />

          {/* Modal Shell */}
          <div className="relative w-full max-w-md overflow-hidden rounded-2xl border border-white/10 shadow-2xl bg-gradient-to-br from-slate-800/95 to-slate-900/95">
            <div className="absolute -inset-0.5 rounded-2xl bg-gradient-to-r from-red-500/20 via-orange-500/20 to-red-500/20 blur opacity-60" />

            {/* Content */}
            <div className="relative flex flex-col">
              {/* Header */}
              <div className="px-6 py-4 border-b border-white/10 flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-red-400" />
                  <h3 className="text-lg font-semibold text-slate-100">Delete Store</h3>
                </div>
                <button 
                  onClick={() => setIsDeleteOpen(false)}
                  disabled={isSubmitting}
                  className="text-slate-400 hover:text-slate-200 rounded-md p-1 transition-colors disabled:opacity-50"
                >
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Body */}
              <div className="p-6">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <svg className="w-6 h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h4 className="text-slate-100 font-medium mb-2">Are you absolutely sure?</h4>
                    <p className="text-sm text-slate-300 mb-3">
                      This will permanently delete the store <span className="font-medium text-slate-100">&quot;{stores.find(s => s.id === deleteStoreId)?.name}&quot;</span> and all associated data.
                    </p>
                    <p className="text-xs text-red-400">
                      This action cannot be undone.
                    </p>
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="px-6 py-4 border-t border-white/10 flex items-center justify-end gap-3">
                <button
                  onClick={() => setIsDeleteOpen(false)}
                  disabled={isSubmitting}
                  className="border border-slate-600 text-slate-300 bg-slate-800/50 hover:bg-slate-700/50 px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteStore}
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-gradient-to-r from-red-600 to-red-500 hover:from-red-500 hover:to-red-400 text-white rounded-lg font-medium shadow-lg shadow-red-500/25 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isSubmitting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                      </svg>
                      Deleting...
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                      Delete Store
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}


    </div>
  );
}


