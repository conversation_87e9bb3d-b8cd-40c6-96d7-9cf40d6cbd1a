import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ToolCallsService } from './tool-calls.service';
import { ToolCall } from './tool-call.entity';

@ApiTags('tool-calls')
@Controller('tool-calls')
export class ToolCallsController {
  constructor(private readonly toolCallsService: ToolCallsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new tool call' })
  @ApiResponse({ status: 201, description: 'Tool call created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  create(@Body() createToolCallDto: Partial<ToolCall>) {
    return this.toolCallsService.create(createToolCallDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all tool calls' })
  @ApiResponse({ status: 200, description: 'Tool calls retrieved successfully' })
  findAll() {
    return this.toolCallsService.findAll();
  }

  @Get('conversation/:conversationId')
  @ApiOperation({ summary: 'Get tool calls by conversation ID' })
  @ApiResponse({ status: 200, description: 'Tool calls retrieved successfully' })
  findByConversationId(@Param('conversationId') conversationId: string) {
    return this.toolCallsService.findByConversationId(conversationId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a tool call by ID' })
  @ApiResponse({ status: 200, description: 'Tool call retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Tool call not found' })
  findOne(@Param('id') id: string) {
    return this.toolCallsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a tool call' })
  @ApiResponse({ status: 200, description: 'Tool call updated successfully' })
  @ApiResponse({ status: 404, description: 'Tool call not found' })
  update(@Param('id') id: string, @Body() updateToolCallDto: Partial<ToolCall>) {
    return this.toolCallsService.update(id, updateToolCallDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a tool call' })
  @ApiResponse({ status: 200, description: 'Tool call deleted successfully' })
  @ApiResponse({ status: 404, description: 'Tool call not found' })
  remove(@Param('id') id: string) {
    return this.toolCallsService.remove(id);
  }
}
