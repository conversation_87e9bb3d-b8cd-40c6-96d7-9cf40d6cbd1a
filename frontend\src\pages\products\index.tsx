import React, { useState, useEffect, useCallback } from 'react';
import Head from 'next/head';
import { productApi } from '../../utils/api';
import { useStore } from '../../context/StoreContext';
import { useAuth } from '../../context/AuthContext';
import EntityTable, { EntityTableColumn } from '../../components/EntityTable';
import TopTaskBar from '../../components/TopTaskBar';
import SideTaskBar from '../../components/SideTaskBar';
import { ProductCreateModal, ProductUpdateModal } from '../../components/ProductModals';
import type { ProductCreateData, ProductUpdateData } from '../../components/ProductModals';
import { useAddEntityShortcut } from '../../utils/hooks';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number | string;
  sku: string;
  imageUrl: string;
  createdAt: string;
  store: {
    id: string;
    name: string;
    currency: string;
  };
}



interface ProductsResponse {
  data: Product[];
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export default function ProductsPage() {
  const { currentStoreId } = useStore();
  const { user } = useAuth();
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const itemsPerPage = 20;

  // Debug logging
  console.log('ProductsPage render - currentStoreId:', currentStoreId, 'user:', user?.id);

  // Keyboard shortcut for F1 to open add modal
  useAddEntityShortcut(
    Boolean(currentStoreId) && !showAddModal,
    () => setShowAddModal(true)
  );

  // Fetch products
  const fetchProducts = useCallback(async (page: number = 1) => {
    if (!currentStoreId) return;
    
    try {
      setIsLoading(true);
      setError(null);
      
      console.log('Fetching products for store:', currentStoreId, 'page:', page);
      
      const response = await productApi.getByStoreId(currentStoreId, {
        page,
        limit: itemsPerPage
      }) as ProductsResponse;
      
      console.log('Products response:', response);
      
      setProducts(response.data);
      setCurrentPage(response.meta.page);
      setTotalPages(response.meta.totalPages);
      setTotalItems(response.meta.total);
    } catch (err) {
      console.error('Error fetching products:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch products');
    } finally {
      setIsLoading(false);
    }
  }, [currentStoreId, itemsPerPage]);

  useEffect(() => {
    if (currentStoreId) {
      fetchProducts(currentPage);
    }
  }, [currentStoreId, currentPage, fetchProducts]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleAddProduct = () => {
    setShowAddModal(true);
  };

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setShowEditModal(true);
  };

  const handleDeleteProduct = async (product: Product) => {
    if (!confirm(`Are you sure you want to delete "${product.name}"?`)) return;
    
    try {
      await productApi.delete(product.id);
      // Refresh the current page
      fetchProducts(currentPage);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete product');
    }
  };

  const handleCreateProduct = async (productData: Omit<ProductCreateData, 'storeId' | 'userId'>) => {
    try {
      await productApi.create({
        ...productData,
        storeId: currentStoreId!,
        userId: user!.id,
      });
      setShowAddModal(false);
      // Refresh the current page
      fetchProducts(currentPage);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create product');
    }
  };

  const handleUpdateProduct = async (productData: ProductUpdateData) => {
    if (!editingProduct) return;
    
    try {
      await productApi.update(editingProduct.id, productData);
      setShowEditModal(false);
      setEditingProduct(null);
      // Refresh the current page
      fetchProducts(currentPage);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update product');
    }
  };

  const formatPrice = (price: number | string, currency: string) => {
    const numericPrice = typeof price === 'string' ? parseFloat(price) : price;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD'
    }).format(numericPrice);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const columns: EntityTableColumn<Product>[] = [
    {
      key: 'name',
      header: 'Product Name',
      render: (value, row) => (
        <div className="flex items-center space-x-3">
          {row.imageUrl && (
            <img 
              src={row.imageUrl} 
              alt={row.name}
              className="w-10 h-10 rounded-lg object-cover bg-slate-700"
            />
          )}
          <div>
            <div className="font-medium text-slate-100">{row.name}</div>
            {row.description && (
              <div className="text-sm text-slate-400 truncate max-w-xs">
                {row.description}
              </div>
            )}
          </div>
        </div>
      )
    },
    {
      key: 'price',
      header: 'Price',
      render: (value, row) => (
        <span className="font-semibold text-emerald-400">
          {formatPrice(row.price, row.store.currency)}
        </span>
      )
    },
    {
      key: 'sku',
      header: 'SKU',
      render: (value) => (
        <span className="text-slate-300 font-mono text-sm">
          {value || '-'}
        </span>
      )
    },
    {
      key: 'store',
      header: 'Store',
      render: (value, row) => (
        <span className="text-slate-300">{row.store.name}</span>
      )
    },
    {
      key: 'createdAt',
      header: 'Created',
      render: (value) => (
        <span className="text-slate-400 text-sm">{formatDate(value)}</span>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (value, row) => (
        <div className="flex items-center space-x-2">
          <button
            className="text-slate-300 hover:text-emerald-400 rounded-md p-2 border border-white/10 hover:bg-white/5"
            title="Edit"
            aria-label="Edit"
            onClick={() => handleEditProduct(row)}
          >
            <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            <span className="sr-only">Edit</span>
          </button>
          <button
            className="text-slate-300 hover:text-red-400 rounded-md p-2 border border-white/10 hover:bg-white/5"
            title="Delete"
            aria-label="Delete"
            onClick={() => handleDeleteProduct(row)}
          >
            <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            <span className="sr-only">Delete</span>
          </button>
        </div>
      )
    }
  ];

  // Early returns for loading/error states
  if (!currentStoreId) {
    return (
      <div className="min-h-screen bg-slate-900">
        <TopTaskBar />
        <div className="flex">
          <SideTaskBar />
          <div className="flex-1 p-8">
            <div className="max-w-md mx-auto text-center">
              <div className="bg-slate-800 rounded-lg shadow-lg p-8 border border-white/10">
                <h2 className="text-xl font-semibold text-slate-100 mb-4">No Store Selected</h2>
                <p className="text-slate-400 mb-6">Please select a store to view products.</p>
                <button
                  onClick={() => window.location.href = '/setup/store'}
                  className="bg-emerald-600 text-white px-4 py-2 rounded-md hover:bg-emerald-700 transition-colors"
                >
                  Create Store
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Products - Teno Store</title>
        <meta name="description" content="Manage your store products" />
      </Head>

      <div className="min-h-screen bg-slate-900">
        <TopTaskBar />
        <div className="flex">
          <SideTaskBar />
          <div className="flex-1 p-8">
            {/* Page Header */}
            <div className="mb-8">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-extrabold tracking-tight text-slate-100">
                    Products
                  </h1>
                  <p className="mt-2 text-slate-400">
                    Manage your store inventory and product catalog
                  </p>
                </div>
                <button
                  onClick={handleAddProduct}
                  className="px-6 py-3 bg-gradient-to-r from-emerald-600 to-emerald-500 hover:from-emerald-500 hover:to-emerald-400 text-white rounded-xl font-medium shadow-lg shadow-emerald-500/25 transition-all duration-200"
                >
                  + Add Product
                </button>
              </div>
            </div>

            {/* Error Display */}
            {error && (
              <div className="mb-6 bg-red-500/10 border border-red-500/20 text-red-300 px-4 py-3 rounded-md">
                {error}
              </div>
            )}

            {/* Products Table */}
            <EntityTable
              columns={columns}
              data={products}
              isLoading={isLoading}
              loadingText="Loading products..."
              noDataText={
                <div className="text-center">
                  <div className="text-slate-400 mb-2">No products found</div>
                  <button
                    onClick={handleAddProduct}
                    className="text-emerald-400 hover:text-emerald-300 underline"
                  >
                    Add your first product
                  </button>
                </div>
              }
              pagination={{
                currentPage,
                totalPages,
                onPageChange: handlePageChange,
                totalItems,
                itemsPerPage,
              }}
            />
          </div>
        </div>

        {/* Product Modals */}
        <ProductCreateModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          onSubmit={handleCreateProduct}
          storeId={currentStoreId!}
        />
        
        <ProductUpdateModal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setEditingProduct(null);
          }}
          onSubmit={handleUpdateProduct}
          product={editingProduct}
        />
      </div>
    </>
  );
}
