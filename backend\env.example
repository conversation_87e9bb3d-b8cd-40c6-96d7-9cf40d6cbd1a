# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password
DB_NAME=teno_store_db

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_CALLBACK_URL=http://localhost:8000/auth/google/callback

# Application Configuration
NODE_ENV=development
PORT=8000
FRONTEND_URL=http://localhost:3000

API_KEY=<api_key>
BASE_URL=https://openrouter.ai/api/v1
MODEL=openai/gpt-4o-mini

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760