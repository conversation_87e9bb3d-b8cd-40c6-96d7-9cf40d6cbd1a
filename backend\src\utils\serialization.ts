/**
 * Utility functions for serializing data for API responses
 */

import { handleTypeORMResult, safeJsonStringify } from './bigint-handler';

/**
 * Recursively converts BigInt values to strings in an object
 * This prevents "Do not know how to serialize a BigInt" errors when using JSON.stringify
 */
export function serializeBigInts<T>(obj: T): T {
  return handleTypeORMResult(obj);
}

/**
 * Safely serializes an object for JSON response, handling BigInt values
 * This is a more robust alternative to JSON.parse(JSON.stringify()) for BigInt handling
 */
export function safeJsonSerialize<T>(obj: T): T {
  return serializeBigInts(obj);
}

/**
 * Safely handles TypeORM query results that might contain BigInt values
 * This is specifically for handling count() and other query results
 */
export function safeQueryResult<T>(result: T): T {
  try {
    // If it's a BigInt (like from count()), convert to number
    if (typeof result === 'bigint') {
      return Number(result) as unknown as T;
    }
    
    // If it's a simple number, return as is
    if (typeof result === 'number') {
      return result;
    }
    
    // If it's an object or array, serialize BigInts
    return serializeBigInts(result);
  } catch (error) {
    console.warn('Error serializing query result:', error);
    // Return the original result if serialization fails
    return result;
  }
}

/**
 * Safely converts count results to numbers
 * This is specifically for handling TypeORM count() results that return BigInt
 */
export function safeCountResult(count: any): number {
  if (typeof count === 'bigint') {
    return Number(count);
  }
  if (typeof count === 'number') {
    return count;
  }
  if (typeof count === 'string') {
    const parsed = parseInt(count, 10);
    return isNaN(parsed) ? 0 : parsed;
  }
  return 0;
}

/**
 * Serializes a store object by converting BigInt fields to strings
 */
export function serializeStore(store: any) {
  if (!store) return store;
  
  return {
    ...store,
    id: store.id?.toString(),
    userId: store.userId?.toString(),
    createdBy: store.createdBy?.toString(),
    updatedBy: store.updatedBy?.toString(),
    owner: store.owner ? {
      ...store.owner,
      id: store.owner.id?.toString(),
    } : null,
  };
}

/**
 * Serializes an array of store objects
 */
export function serializeStores(stores: any[]) {
  return stores.map(serializeStore);
}

/**
 * Serializes an agent object by converting BigInt fields to strings
 */
export function serializeAgent(agent: any) {
  if (!agent) return agent;
  
  return {
    ...agent,
    id: agent.id?.toString(),
    userId: agent.userId?.toString(),
    storeId: agent.storeId?.toString(),
    createdBy: agent.createdBy?.toString(),
    updatedBy: agent.updatedBy?.toString(),
    user: agent.user ? {
      id: agent.user.id?.toString(),
      name: agent.user.name,
      email: agent.user.email,
    } : null,
    store: agent.store ? {
      id: agent.store.id?.toString(),
      name: agent.store.name,
      uuid: agent.store.uuid,
    } : null,
    createdByUser: agent.createdByUser ? {
      id: agent.createdByUser.id?.toString(),
      name: agent.createdByUser.name,
      email: agent.createdByUser.email,
    } : null,
    updatedByUser: agent.updatedByUser ? {
      id: agent.updatedByUser.id?.toString(),
      name: agent.updatedByUser.name,
      email: agent.updatedByUser.email,
    } : null,
  };
}

/**
 * Serializes an array of agent objects
 */
export function serializeAgents(agents: any[]) {
  return agents.map(serializeAgent);
}

/**
 * Serializes a product object by converting BigInt fields to strings
 */
export function serializeProduct(product: any) {
  if (!product) return product;
  
  return {
    ...product,
    id: product.id?.toString(),
    userId: product.userId?.toString(),
    storeId: product.storeId?.toString(),
    createdBy: product.createdBy?.toString(),
    updatedBy: product.updatedBy?.toString(),
    price: typeof product.price === 'string' ? parseFloat(product.price) : product.price,
    store: product.store ? serializeStore(product.store) : null,
  };
}

/**
 * Serializes an array of product objects
 */
export function serializeProducts(products: any[]) {
  return products.map(serializeProduct);
}

/**
 * Serializes a customer object by converting BigInt fields to strings
 */
export function serializeCustomer(customer: any) {
  if (!customer) return customer;
  
  return {
    ...customer,
    id: customer.id?.toString(),
    userId: customer.userId?.toString(),
    storeId: customer.storeId?.toString(),
    createdBy: customer.createdBy?.toString(),
    updatedBy: customer.updatedBy?.toString(),
    store: customer.store ? serializeStore(customer.store) : null,
  };
}

/**
 * Serializes an array of customer objects
 */
export function serializeCustomers(customers: any[]) {
  return customers.map(serializeCustomer);
}

/**
 * Serializes an order object by converting BigInt fields to strings
 */
export function serializeOrder(order: any) {
  if (!order) return order;
  
  return {
    ...order,
    id: order.id?.toString(),
    userId: order.userId?.toString(),
    storeId: order.storeId?.toString(),
    customerId: order.customerId?.toString(),
    createdBy: order.createdBy?.toString(),
    updatedBy: order.updatedBy?.toString(),
    store: order.store ? serializeStore(order.store) : null,
    customer: order.customer ? serializeCustomer(order.customer) : null,
    items: order.items ? order.items.map((item: any) => ({
      ...item,
      id: item.id?.toString(),
      orderId: item.orderId?.toString(),
      productId: item.productId?.toString(),
      product: item.product ? {
        ...item.product,
        id: item.product.id?.toString(),
      } : null,
    })) : [],
  };
}

/**
 * Serializes an array of order objects
 */
export function serializeOrders(orders: any[]) {
  return orders.map(serializeOrder);
}

/**
 * Serializes a conversation object by converting BigInt fields to strings
 */
export function serializeConversation(conversation: any) {
  if (!conversation) return conversation;
  
  return {
    ...conversation,
    id: conversation.id?.toString(),
    userId: conversation.userId?.toString(),
    storeId: conversation.storeId?.toString(),
    customerId: conversation.customerId?.toString(),
    createdBy: conversation.createdBy?.toString(),
    updatedBy: conversation.updatedBy?.toString(),
    // Ensure numeric fields are properly converted to numbers
    totalCost: conversation.totalCost !== null ? Number(conversation.totalCost) : null,
    totalExecutionTime: conversation.totalExecutionTime !== null ? Number(conversation.totalExecutionTime) : null,
    totalInputTokens: conversation.totalInputTokens !== null ? Number(conversation.totalInputTokens) : null,
    totalOutputTokens: conversation.totalOutputTokens !== null ? Number(conversation.totalOutputTokens) : null,
    store: conversation.store ? serializeStore(conversation.store) : null,
    user: conversation.user ? {
      ...conversation.user,
      id: conversation.user.id?.toString(),
    } : null,
    customer: conversation.customer ? serializeCustomer(conversation.customer) : null,
  };
}

/**
 * Serializes an array of conversation objects
 */
export function serializeConversations(conversations: any[]) {
  return conversations.map(serializeConversation);
}
