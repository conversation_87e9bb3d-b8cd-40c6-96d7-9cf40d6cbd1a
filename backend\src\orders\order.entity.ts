import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany } from 'typeorm';
import { Currency } from '../shared/enums';

export enum OrderStatus {
  draft = 'draft',
  pending = 'pending',
  confirmed = 'confirmed',
  processing = 'processing',
  shipped = 'shipped',
  delivered = 'delivered',
  cancelled = 'cancelled',
  returned = 'returned'
}

export enum OrderPriority {
  low = 'low',
  normal = 'normal',
  high = 'high',
  urgent = 'urgent'
}

@Entity('orders')
export class Order {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: string;

  @Column({ type: 'enum', enum: OrderStatus, enumName: 'orders_status_enum', default: OrderStatus.draft })
  status: OrderStatus;

  @Column({ type: 'enum', enum: OrderPriority, enumName: 'orders_priority_enum', default: OrderPriority.normal })
  priority: OrderPriority;

  @Column({ type: 'varchar', unique: true })
  orderNumber: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  subtotal: number;

  @Column({ type: 'boolean', default: false })
  useTax: boolean;

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0 })
  taxRate: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  taxAmount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  total: number;

  @Column({ type: 'enum', enum: Currency, enumName: 'currency_enum', default: Currency.USD })
  currency: Currency;

  @Column({ type: 'text', nullable: true })
  cancellationReason: string;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  orderDate: Date;

  @Column({ nullable: true, type: 'timestamp' })
  expectedDeliveryDate: Date;

  @Column({ type: 'varchar', nullable: true })
  preferredDeliveryLocation: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'boolean', default: false })
  isDeleted: boolean;

  @Column({ type: 'bigint' })
  createdBy: string;

  @Column({ nullable: true, type: 'bigint' })
  updatedBy: string;

  @Column({ type: 'bigint' })
  userId: string;

  @Column({ type: 'bigint' })
  storeId: string;

  @Column({ type: 'bigint' })
  customerId: string;

  // Relations - using string literals to avoid circular dependency
  @ManyToOne('Store', (store: any) => store.orders)
  store: any;

  @ManyToOne('Customer', (customer: any) => customer.orders)
  customer: any;

  @ManyToOne('User', (user: any) => user.createdOrders)
  createdByUser: any;

  @ManyToOne('User', (user: any) => user.updatedOrders)
  updatedByUser: any;

  @ManyToOne('User', (user: any) => user.orders)
  user: any;

  @OneToMany('OrderItem', (orderItem: any) => orderItem.order)
  orderItems: any[];
}
