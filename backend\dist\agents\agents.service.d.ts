import { Repository } from 'typeorm';
import { Agent } from './agent.entity';
export declare class AgentsService {
    private agentsRepository;
    constructor(agentsRepository: Repository<Agent>);
    findAll(): Promise<Agent[]>;
    findOne(id: string): Promise<Agent>;
    findByStoreId(storeId: string): Promise<Agent[]>;
    findByUserId(userId: string): Promise<Agent[]>;
    create(createAgentDto: Partial<Agent>): Promise<Agent>;
    update(id: string, updateAgentDto: Partial<Agent>): Promise<Agent>;
    remove(id: string): Promise<void>;
}
