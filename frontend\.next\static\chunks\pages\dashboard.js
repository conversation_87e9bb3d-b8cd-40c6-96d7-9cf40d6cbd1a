/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/dashboard"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cteno-store%5Cfrontend%5Csrc%5Cpages%5Cdashboard.tsx&page=%2Fdashboard!":
/*!************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cteno-store%5Cfrontend%5Csrc%5Cpages%5Cdashboard.tsx&page=%2Fdashboard! ***!
  \************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/dashboard\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/dashboard.tsx */ \"./src/pages/dashboard.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/dashboard\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1DJTNBJTVDVXNlcnMlNUNtYWhsbCU1Q0RvY3VtZW50cyU1Q3dvcmtzcGFjZSU1Q3Byb2plY3RzJTVDdGVuby1zdG9yZSU1Q2Zyb250ZW5kJTVDc3JjJTVDcGFnZXMlNUNkYXNoYm9hcmQudHN4JnBhZ2U9JTJGZGFzaGJvYXJkISIsIm1hcHBpbmdzIjoiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBTyxDQUFDLDREQUEyQjtBQUNsRDtBQUNBO0FBQ0EsT0FBTyxJQUFVO0FBQ2pCLE1BQU0sVUFBVTtBQUNoQjtBQUNBLE9BQU87QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/ZWY5YiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL2Rhc2hib2FyZFwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vc3JjL3BhZ2VzL2Rhc2hib2FyZC50c3hcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL2Rhc2hib2FyZFwiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cteno-store%5Cfrontend%5Csrc%5Cpages%5Cdashboard.tsx&page=%2Fdashboard!\n"));

/***/ }),

/***/ "./src/components/SideTaskBar.tsx":
/*!****************************************!*\
  !*** ./src/components/SideTaskBar.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SideTaskBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/AuthContext */ \"./src/context/AuthContext.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction SideTaskBar() {\n    var _user_name, _user_email, _user_email1;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const navigationSections = [\n        {\n            name: \"Overview\",\n            items: [\n                {\n                    id: \"dashboard\",\n                    name: \"Dashboard\",\n                    href: \"/dashboard\",\n                    description: \"Overview of your store metrics\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 13\n                    }, this)\n                }\n            ]\n        },\n        {\n            name: \"Communication\",\n            items: [\n                {\n                    id: \"conversations\",\n                    name: \"Conversations\",\n                    href: \"/conversations\",\n                    description: \"Agent conversations with customers\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 13\n                    }, this)\n                }\n            ]\n        },\n        {\n            name: \"Store Management\",\n            items: [\n                {\n                    id: \"products\",\n                    name: \"Products\",\n                    href: \"/products\",\n                    description: \"Manage your product catalog\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    id: \"inventory\",\n                    name: \"Inventory\",\n                    href: \"/inventory\",\n                    description: \"Track stock levels and movements\",\n                    badge: \"Soon\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    id: \"customers\",\n                    name: \"Customers\",\n                    href: \"/customers\",\n                    description: \"Manage customer relationships\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 13\n                    }, this)\n                }\n            ]\n        },\n        {\n            name: \"Sales & Orders\",\n            items: [\n                {\n                    id: \"orders\",\n                    name: \"Orders\",\n                    href: \"/orders\",\n                    description: \"Process and manage orders\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    id: \"sales\",\n                    name: \"Sales\",\n                    href: \"/sales\",\n                    description: \"View sales analytics and reports\",\n                    badge: \"Soon\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 13\n                    }, this)\n                }\n            ]\n        },\n        {\n            name: \"Supply Chain\",\n            items: [\n                {\n                    id: \"purchases\",\n                    name: \"Purchases\",\n                    href: \"/purchases\",\n                    description: \"Manage purchase orders\",\n                    badge: \"Soon\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v5a2 2 0 01-2 2H9a2 2 0 01-2-2v-5m6-5V7a2 2 0 00-2-2H9a2 2 0 00-2 2v1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    id: \"suppliers\",\n                    name: \"Suppliers\",\n                    href: \"/suppliers\",\n                    description: \"Manage supplier relationships\",\n                    badge: \"Soon\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 13\n                    }, this)\n                }\n            ]\n        }\n    ];\n    const isActiveRoute = (href)=>{\n        if (href === \"/dashboard\") {\n            return router.pathname === \"/dashboard\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    const handleNavigation = (item)=>{\n        if (item.badge === \"Soon\") {\n            // Show coming soon notification\n            return;\n        }\n        if (item.isExternal) {\n            window.open(item.href, \"_blank\");\n        } else {\n            router.push(item.href);\n        }\n    };\n    if (!user) {\n        return null; // Don't show sidebar if not authenticated\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 lg:hidden\",\n                \"aria-hidden\": \"true\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-gray-600 bg-opacity-75\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-0 left-0 z-50 h-full bg-slate-900 border-r border-slate-700/50 transition-all duration-300 ease-in-out \".concat(isCollapsed ? \"w-16\" : \"w-64\", \" flex flex-col\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 border-b border-slate-700/50\",\n                        children: [\n                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 rounded-lg bg-gradient-to-br from-emerald-500 to-cyan-500 flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-white\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-bold bg-gradient-to-r from-emerald-400 to-cyan-400 bg-clip-text text-transparent\",\n                                        children: \"Navigation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsCollapsed(!isCollapsed),\n                                className: \"p-2 rounded-lg bg-slate-800/60 border border-slate-600/50 text-slate-400 hover:text-white hover:bg-slate-700/60 transition-all duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 transition-transform duration-200 \".concat(isCollapsed ? \"rotate-180\" : \"\"),\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M11 19l-7-7 7-7m8 14l-7-7 7-7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto py-4 space-y-6\",\n                        children: navigationSections.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-3\",\n                                children: [\n                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"px-3 text-xs font-semibold text-slate-400 uppercase tracking-wider mb-3\",\n                                        children: section.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"space-y-1\",\n                                        children: section.items.map((item)=>{\n                                            const isActive = isActiveRoute(item.href);\n                                            const isComingSoon = item.badge === \"Soon\";\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleNavigation(item),\n                                                disabled: isComingSoon,\n                                                className: \"group w-full flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 \".concat(isActive ? \"bg-emerald-600/20 border border-emerald-500/30 text-emerald-400\" : isComingSoon ? \"text-slate-500 cursor-not-allowed\" : \"text-slate-300 hover:bg-slate-800/60 hover:text-white\"),\n                                                title: isCollapsed ? item.name : item.description,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0 \".concat(isActive ? \"text-emerald-400\" : isComingSoon ? \"text-slate-500\" : \"text-slate-400 group-hover:text-white\"),\n                                                        children: item.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-3 flex-1 text-left\",\n                                                                children: item.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 px-2 py-0.5 text-xs rounded-full font-medium \".concat(item.badge === \"Soon\" ? \"bg-yellow-900/30 text-yellow-400 border border-yellow-500/30\" : \"bg-emerald-900/30 text-emerald-400 border border-emerald-500/30\"),\n                                                                children: item.badge\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-emerald-400 flex-shrink-0\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M9 5l7 7-7 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                                                    lineNumber: 262,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true),\n                                                    isCollapsed && item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute left-14 w-2 h-2 bg-yellow-400 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, item.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, section.name, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this),\n                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t border-slate-700/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-slate-800/60 rounded-lg p-3 border border-slate-600/50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 rounded-lg bg-gradient-to-br from-violet-500 to-purple-600 flex items-center justify-center text-white text-sm font-bold shadow-sm flex-shrink-0\",\n                                        children: user ? (((_user_name = user.name) === null || _user_name === void 0 ? void 0 : _user_name[0]) || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email[0]) || \"U\").toUpperCase() : \"G\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-white truncate\",\n                                                children: user ? user.name || ((_user_email1 = user.email) === null || _user_email1 === void 0 ? void 0 : _user_email1.split(\"@\")[0]) : \"Guest\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-slate-400 truncate\",\n                                                children: user ? user.email : \"Not signed in\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"transition-all duration-300 ease-in-out \".concat(isCollapsed ? \"ml-16\" : \"ml-64\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\SideTaskBar.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(SideTaskBar, \"rufV/VgLKVri3yiZuJNUlO+NqTE=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = SideTaskBar;\nvar _c;\n$RefreshReg$(_c, \"SideTaskBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/SideTaskBar.tsx\n"));

/***/ }),

/***/ "./src/components/TopTaskBar.tsx":
/*!***************************************!*\
  !*** ./src/components/TopTaskBar.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TopTaskBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/api */ \"./src/utils/api.ts\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _context_StoreContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../context/StoreContext */ \"./src/context/StoreContext.tsx\");\n/* harmony import */ var _context_PreferencesContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../context/PreferencesContext */ \"./src/context/PreferencesContext.tsx\");\n/* harmony import */ var _utils_preferences__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../utils/preferences */ \"./src/utils/preferences.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Currency names mapping for display labels\nconst CURRENCY_NAMES = {\n    \"USD\": \"US Dollar\",\n    \"EUR\": \"Euro\",\n    \"GBP\": \"British Pound\",\n    \"CAD\": \"Canadian Dollar\",\n    \"AUD\": \"Australian Dollar\",\n    \"NZD\": \"New Zealand Dollar\",\n    \"JPY\": \"Japanese Yen\",\n    \"CNY\": \"Chinese Yuan\",\n    \"HKD\": \"Hong Kong Dollar\",\n    \"SGD\": \"Singapore Dollar\",\n    \"INR\": \"Indian Rupee\",\n    \"BRL\": \"Brazilian Real\",\n    \"MXN\": \"Mexican Peso\",\n    \"ZAR\": \"South African Rand\",\n    \"SEK\": \"Swedish Krona\",\n    \"NOK\": \"Norwegian Krone\",\n    \"DKK\": \"Danish Krone\",\n    \"CHF\": \"Swiss Franc\",\n    \"PLN\": \"Polish Zloty\",\n    \"CZK\": \"Czech Koruna\",\n    \"HUF\": \"Hungarian Forint\",\n    \"ILS\": \"Israeli Shekel\",\n    \"TRY\": \"Turkish Lira\",\n    \"AED\": \"UAE Dirham\",\n    \"SAR\": \"Saudi Riyal\",\n    \"DZD\": \"Algerian Dinar\",\n    \"QAR\": \"Qatari Riyal\",\n    \"KWD\": \"Kuwaiti Dinar\",\n    \"BHD\": \"Bahraini Dinar\",\n    \"OMR\": \"Omani Rial\",\n    \"EGP\": \"Egyptian Pound\",\n    \"NGN\": \"Nigerian Naira\",\n    \"KES\": \"Kenyan Shilling\",\n    \"ARS\": \"Argentine Peso\",\n    \"CLP\": \"Chilean Peso\",\n    \"COP\": \"Colombian Peso\",\n    \"PEN\": \"Peruvian Sol\",\n    \"UYU\": \"Uruguayan Peso\",\n    \"KRW\": \"South Korean Won\",\n    \"THB\": \"Thai Baht\",\n    \"MYR\": \"Malaysian Ringgit\",\n    \"PHP\": \"Philippine Peso\",\n    \"IDR\": \"Indonesian Rupiah\"\n};\n// Generate currency options from centralized symbols\nconst currencyOptions = _utils_preferences__WEBPACK_IMPORTED_MODULE_7__.CURRENCY_OPTIONS.map((currency)=>({\n        value: currency,\n        label: \"\".concat(CURRENCY_NAMES[currency], \" (\").concat(currency, \")\"),\n        symbol: _utils_preferences__WEBPACK_IMPORTED_MODULE_7__.CURRENCY_SYMBOLS[currency]\n    }));\n// Language names mapping for display labels\nconst LANGUAGE_NAMES = {\n    \"en\": {\n        label: \"English\",\n        nativeLabel: \"English\"\n    },\n    \"es\": {\n        label: \"Spanish\",\n        nativeLabel: \"Espa\\xf1ol\"\n    },\n    \"fr\": {\n        label: \"French\",\n        nativeLabel: \"Fran\\xe7ais\"\n    },\n    \"de\": {\n        label: \"German\",\n        nativeLabel: \"Deutsch\"\n    },\n    \"it\": {\n        label: \"Italian\",\n        nativeLabel: \"Italiano\"\n    },\n    \"pt\": {\n        label: \"Portuguese\",\n        nativeLabel: \"Portugu\\xeas\"\n    },\n    \"ru\": {\n        label: \"Russian\",\n        nativeLabel: \"Русский\"\n    },\n    \"zh\": {\n        label: \"Chinese\",\n        nativeLabel: \"中文\"\n    },\n    \"ja\": {\n        label: \"Japanese\",\n        nativeLabel: \"日本語\"\n    },\n    \"ko\": {\n        label: \"Korean\",\n        nativeLabel: \"한국어\"\n    },\n    \"ar\": {\n        label: \"Arabic\",\n        nativeLabel: \"العربية\"\n    },\n    \"hi\": {\n        label: \"Hindi\",\n        nativeLabel: \"हिन्दी\"\n    },\n    \"bn\": {\n        label: \"Bengali\",\n        nativeLabel: \"বাংলা\"\n    },\n    \"pa\": {\n        label: \"Punjabi\",\n        nativeLabel: \"ਪੰਜਾਬੀ\"\n    },\n    \"ur\": {\n        label: \"Urdu\",\n        nativeLabel: \"اردو\"\n    },\n    \"fa\": {\n        label: \"Persian\",\n        nativeLabel: \"فارسی\"\n    },\n    \"tr\": {\n        label: \"Turkish\",\n        nativeLabel: \"T\\xfcrk\\xe7e\"\n    },\n    \"nl\": {\n        label: \"Dutch\",\n        nativeLabel: \"Nederlands\"\n    },\n    \"sv\": {\n        label: \"Swedish\",\n        nativeLabel: \"Svenska\"\n    },\n    \"no\": {\n        label: \"Norwegian\",\n        nativeLabel: \"Norsk\"\n    },\n    \"da\": {\n        label: \"Danish\",\n        nativeLabel: \"Dansk\"\n    },\n    \"fi\": {\n        label: \"Finnish\",\n        nativeLabel: \"Suomi\"\n    },\n    \"pl\": {\n        label: \"Polish\",\n        nativeLabel: \"Polski\"\n    },\n    \"cs\": {\n        label: \"Czech\",\n        nativeLabel: \"Čeština\"\n    },\n    \"sk\": {\n        label: \"Slovak\",\n        nativeLabel: \"Slovenčina\"\n    },\n    \"hu\": {\n        label: \"Hungarian\",\n        nativeLabel: \"Magyar\"\n    },\n    \"ro\": {\n        label: \"Romanian\",\n        nativeLabel: \"Rom\\xe2nă\"\n    },\n    \"bg\": {\n        label: \"Bulgarian\",\n        nativeLabel: \"Български\"\n    },\n    \"hr\": {\n        label: \"Croatian\",\n        nativeLabel: \"Hrvatski\"\n    },\n    \"sl\": {\n        label: \"Slovenian\",\n        nativeLabel: \"Slovenščina\"\n    }\n};\n// Generate language options from centralized options\nconst languageOptions = _utils_preferences__WEBPACK_IMPORTED_MODULE_7__.LANGUAGE_OPTIONS.map((lang)=>{\n    var _LANGUAGE_NAMES_lang, _LANGUAGE_NAMES_lang1;\n    return {\n        value: lang,\n        label: ((_LANGUAGE_NAMES_lang = LANGUAGE_NAMES[lang]) === null || _LANGUAGE_NAMES_lang === void 0 ? void 0 : _LANGUAGE_NAMES_lang.label) || lang,\n        nativeLabel: ((_LANGUAGE_NAMES_lang1 = LANGUAGE_NAMES[lang]) === null || _LANGUAGE_NAMES_lang1 === void 0 ? void 0 : _LANGUAGE_NAMES_lang1.nativeLabel) || lang\n    };\n});\nfunction TopTaskBar() {\n    var _stores_find;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { currency: preferredCurrency, language: preferredLanguage, setCurrency, setLanguage } = (0,_context_PreferencesContext__WEBPACK_IMPORTED_MODULE_6__.usePreferences)();\n    const { currentStoreId, setCurrentStoreId, autoSelectFirstStore } = (0,_context_StoreContext__WEBPACK_IMPORTED_MODULE_5__.useStore)();\n    // State for stores\n    const [stores, setStores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [storesLoading, setStoresLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State for modals and menu\n    const [isStoreModalOpen, setIsStoreModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [modalMode, setModalMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"create\");\n    const [isDeleteOpen, setIsDeleteOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStoreMenuOpen, setIsStoreMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State for forms\n    const [storeForm, setStoreForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        description: \"\",\n        currency: preferredCurrency,\n        preferredLanguage: preferredLanguage,\n        userId: (user === null || user === void 0 ? void 0 : user.id) || \"\"\n    });\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingStoreId, setEditingStoreId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteStoreId, setDeleteStoreId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Refs for focus management\n    const storeNameInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const storeMenuRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Fetch stores when user is available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user === null || user === void 0 ? void 0 : user.id) {\n            fetchStores();\n        }\n    }, [\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    // Focus management for modals\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isStoreModalOpen && storeNameInputRef.current) {\n            storeNameInputRef.current.focus();\n        }\n    }, [\n        isStoreModalOpen\n    ]);\n    // Click outside to close store menu\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (storeMenuRef.current && !storeMenuRef.current.contains(event.target)) {\n                setIsStoreMenuOpen(false);\n            }\n        };\n        if (isStoreMenuOpen) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n            return ()=>document.removeEventListener(\"mousedown\", handleClickOutside);\n        }\n    }, [\n        isStoreMenuOpen\n    ]);\n    // Escape key to close menu\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleEscape = (event)=>{\n            if (event.key === \"Escape\" && isStoreMenuOpen) {\n                setIsStoreMenuOpen(false);\n            }\n        };\n        if (isStoreMenuOpen) {\n            document.addEventListener(\"keydown\", handleEscape);\n            return ()=>document.removeEventListener(\"keydown\", handleEscape);\n        }\n    }, [\n        isStoreMenuOpen\n    ]);\n    // Close menu after actions\n    const handleCreateModalOpen = ()=>{\n        openCreateModal();\n        setIsStoreMenuOpen(false);\n    };\n    const handleEditModalOpen = (store)=>{\n        openEditModal(store);\n        setIsStoreMenuOpen(false);\n    };\n    const handleDeleteModalOpen = (store)=>{\n        openDeleteModal(store);\n        setIsStoreMenuOpen(false);\n    };\n    const fetchStores = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.id)) return;\n        setStoresLoading(true);\n        try {\n            const data = await _utils_api__WEBPACK_IMPORTED_MODULE_3__.storeApi.getByUserId(user.id, {\n                page: 1,\n                limit: 100\n            });\n            const storesData = (data === null || data === void 0 ? void 0 : data.data) || [];\n            setStores(storesData);\n            // Auto-select the first store if none is currently selected\n            autoSelectFirstStore(storesData);\n        } catch (error) {\n            console.error(\"Failed to fetch stores:\", error);\n        } finally{\n            setStoresLoading(false);\n        }\n    };\n    const validateForm = ()=>{\n        const errors = {};\n        if (!storeForm.name.trim()) {\n            errors.name = \"Store name is required\";\n        } else if (storeForm.name.trim().length < 2) {\n            errors.name = \"Store name must be at least 2 characters\";\n        } else if (storeForm.name.trim().length > 50) {\n            errors.name = \"Store name must be less than 50 characters\";\n        }\n        if (storeForm.description.trim().length > 200) {\n            errors.description = \"Description must be less than 200 characters\";\n        }\n        if (!storeForm.currency) {\n            errors.currency = \"Currency is required\";\n        }\n        if (!storeForm.preferredLanguage) {\n            errors.preferredLanguage = \"Language is required\";\n        }\n        return errors;\n    };\n    const resetForm = ()=>{\n        setStoreForm({\n            name: \"\",\n            description: \"\",\n            currency: preferredCurrency,\n            preferredLanguage: preferredLanguage,\n            userId: (user === null || user === void 0 ? void 0 : user.id) || \"\"\n        });\n        setFormErrors({});\n        setEditingStoreId(null);\n    };\n    const openCreateModal = ()=>{\n        resetForm();\n        setModalMode(\"create\");\n        setIsStoreModalOpen(true);\n    };\n    const openEditModal = (store)=>{\n        setStoreForm({\n            name: store.name,\n            description: store.description || \"\",\n            currency: store.currency,\n            preferredLanguage: store.preferredLanguage,\n            userId: (user === null || user === void 0 ? void 0 : user.id) || \"\"\n        });\n        setEditingStoreId(store.id);\n        setModalMode(\"edit\");\n        setFormErrors({});\n        setIsStoreModalOpen(true);\n    };\n    const handleSubmitStore = async ()=>{\n        const errors = validateForm();\n        setFormErrors(errors);\n        if (Object.keys(errors).length > 0) {\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            if (modalMode === \"create\") {\n                await _utils_api__WEBPACK_IMPORTED_MODULE_3__.storeApi.create({\n                    ...storeForm,\n                    userId: (user === null || user === void 0 ? void 0 : user.id) || \"\"\n                });\n            } else {\n                await _utils_api__WEBPACK_IMPORTED_MODULE_3__.storeApi.update(editingStoreId, storeForm);\n            }\n            setIsStoreModalOpen(false);\n            resetForm();\n            fetchStores();\n        } catch (error) {\n            console.error(\"Failed to \".concat(modalMode, \" store:\"), error);\n        // You might want to show a toast notification here\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const openDeleteModal = (store)=>{\n        setDeleteStoreId(store.id);\n        setIsDeleteOpen(true);\n    };\n    const handleDeleteStore = async ()=>{\n        if (!deleteStoreId) return;\n        setIsSubmitting(true);\n        try {\n            await _utils_api__WEBPACK_IMPORTED_MODULE_3__.storeApi.delete(deleteStoreId);\n            setIsDeleteOpen(false);\n            setDeleteStoreId(null);\n            fetchStores();\n            // If we deleted the current store, clear it\n            if (currentStoreId === deleteStoreId) {\n                setCurrentStoreId(null);\n            }\n        } catch (error) {\n            console.error(\"Failed to delete store:\", error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleLogout = async ()=>{\n        try {\n            await logout();\n            router.replace(\"/login?loggedOut=1\");\n        } catch (error) {\n            console.error(\"Logout failed:\", error);\n        }\n    };\n    const currentStore = stores.find((store)=>store.id === currentStoreId);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-slate-900 shadow-sm border-b border-white/10\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-slate-100\",\n                                        children: \"Teno Store\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    ref: storeMenuRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsStoreMenuOpen(!isStoreMenuOpen),\n                                            className: \"inline-flex items-center px-4 py-2 border border-slate-600 text-sm leading-4 font-medium rounded-md text-slate-300 bg-slate-800 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-colors min-w-[220px]\",\n                                            disabled: storesLoading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 mr-2\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex-1 text-left truncate\",\n                                                    children: storesLoading ? \"Loading...\" : currentStore ? currentStore.name : \"Select Store\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-2 transition-transform \".concat(isStoreMenuOpen ? \"rotate-180\" : \"\"),\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M19 9l-7 7-7-7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this),\n                                        isStoreMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute left-0 mt-2 w-full min-w-[280px] bg-slate-800 border border-slate-600 rounded-lg shadow-xl z-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleCreateModalOpen,\n                                                        className: \"flex items-center px-4 py-3 text-sm text-emerald-400 hover:bg-slate-700 hover:text-emerald-300 w-full text-left transition-colors border-b border-slate-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Create New Store\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                        lineNumber: 401,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-slate-400\",\n                                                                        children: \"Set up a new store\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    stores.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-4 py-2 border-b border-slate-600\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-slate-400 uppercase tracking-wide font-medium\",\n                                                                    children: \"Select Store\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"max-h-60 overflow-y-auto\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            setCurrentStoreId(null);\n                                                                            setIsStoreMenuOpen(false);\n                                                                        },\n                                                                        className: \"flex items-center px-4 py-2 text-sm w-full text-left transition-colors \".concat(!currentStoreId ? \"bg-slate-700 text-slate-100\" : \"text-slate-300 hover:bg-slate-700 hover:text-slate-100\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 rounded-full bg-slate-500 mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                lineNumber: 427,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"italic\",\n                                                                                children: \"No store selected\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                lineNumber: 428,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            !currentStoreId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4 ml-auto\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M5 13l4 4L19 7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                    lineNumber: 431,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                lineNumber: 430,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    stores.map((store)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative group\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>{\n                                                                                        setCurrentStoreId(store.id);\n                                                                                        setIsStoreMenuOpen(false);\n                                                                                    },\n                                                                                    className: \"flex items-center px-4 py-2 text-sm w-full text-left transition-colors \".concat(currentStoreId === store.id ? \"bg-emerald-600/20 text-emerald-200\" : \"text-slate-300 hover:bg-slate-700 hover:text-slate-100\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"w-2 h-2 rounded-full mr-3 \".concat(currentStoreId === store.id ? \"bg-emerald-400\" : \"bg-slate-400\")\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                            lineNumber: 450,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex-1 min-w-0\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"font-medium truncate\",\n                                                                                                    children: store.name\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                                    lineNumber: 454,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                store.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-xs text-slate-400 truncate\",\n                                                                                                    children: store.description\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                                    lineNumber: 456,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                            lineNumber: 453,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        currentStoreId === store.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4 ml-2\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M5 13l4 4L19 7\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                                lineNumber: 461,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                            lineNumber: 460,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                    lineNumber: 439,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                currentStoreId === store.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: (e)=>{\n                                                                                                e.stopPropagation();\n                                                                                                handleEditModalOpen(store);\n                                                                                            },\n                                                                                            className: \"p-1 rounded text-slate-400 hover:text-slate-200 hover:bg-slate-600 transition-colors\",\n                                                                                            title: \"Edit store\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                className: \"w-3 h-3\",\n                                                                                                fill: \"none\",\n                                                                                                stroke: \"currentColor\",\n                                                                                                viewBox: \"0 0 24 24\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                    strokeLinecap: \"round\",\n                                                                                                    strokeLinejoin: \"round\",\n                                                                                                    strokeWidth: 2,\n                                                                                                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                                    lineNumber: 478,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                                lineNumber: 477,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                            lineNumber: 469,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: (e)=>{\n                                                                                                e.stopPropagation();\n                                                                                                handleDeleteModalOpen(store);\n                                                                                            },\n                                                                                            className: \"p-1 rounded text-slate-400 hover:text-red-400 hover:bg-slate-600 transition-colors\",\n                                                                                            title: \"Delete store\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                className: \"w-3 h-3\",\n                                                                                                fill: \"none\",\n                                                                                                stroke: \"currentColor\",\n                                                                                                viewBox: \"0 0 24 24\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                    strokeLinecap: \"round\",\n                                                                                                    strokeLinejoin: \"round\",\n                                                                                                    strokeWidth: 2,\n                                                                                                    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                                    lineNumber: 490,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                                lineNumber: 489,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                            lineNumber: 481,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                    lineNumber: 468,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, store.id, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                            lineNumber: 438,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true),\n                                                    stores.length === 0 && !storesLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"px-4 py-6 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-8 h-8 mx-auto text-slate-500 mb-2\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                lineNumber: 504,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-slate-400 mb-2\",\n                                                                children: \"No stores found\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-slate-500\",\n                                                                children: \"Create your first store to get started\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                lineNumber: 508,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: preferredCurrency,\n                                        onChange: (e)=>setCurrency(e.target.value),\n                                        className: \"block w-full pl-3 pr-10 py-2 text-base border-slate-600 bg-slate-800 text-slate-100 focus:outline-none focus:ring-2 focus:ring-emerald-400/50 focus:ring-emerald-400/50 sm:text-sm rounded-md\",\n                                        children: currencyOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: option.value,\n                                                children: [\n                                                    option.symbol,\n                                                    \" \",\n                                                    option.label\n                                                ]\n                                            }, option.value, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: preferredLanguage,\n                                        onChange: (e)=>setLanguage(e.target.value),\n                                        className: \"block w-full pl-3 pr-10 py-2 text-base border-slate-600 bg-slate-800 text-slate-100 focus:outline-none focus:ring-2 focus:ring-emerald-400/50 focus:border-emerald-400/50 sm:text-sm rounded-md\",\n                                        children: languageOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: option.value,\n                                                children: option.nativeLabel\n                                            }, option.value, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-slate-300\",\n                                                children: user === null || user === void 0 ? void 0 : user.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleLogout,\n                                                className: \"text-sm text-slate-400 hover:text-slate-200 transition-colors\",\n                                                children: \"Logout\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                    lineNumber: 364,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                lineNumber: 363,\n                columnNumber: 7\n            }, this),\n            isStoreModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/50\",\n                        onClick: ()=>setIsStoreModalOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                        lineNumber: 568,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full max-w-lg overflow-hidden rounded-2xl border border-white/10 shadow-2xl bg-gradient-to-br from-slate-800/95 to-slate-900/95\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -inset-0.5 rounded-2xl bg-gradient-to-r from-emerald-500/20 via-cyan-500/20 to-indigo-500/20 blur opacity-60\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                lineNumber: 572,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex flex-col max-h-[90vh]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 border-b border-white/10 flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full \".concat(modalMode === \"create\" ? \"bg-emerald-400\" : \"bg-blue-400\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-slate-100\",\n                                                        children: modalMode === \"create\" ? \"Create New Store\" : \"Edit Store\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsStoreModalOpen(false),\n                                                className: \"text-slate-400 hover:text-slate-200 rounded-md p-1 transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                    lineNumber: 588,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 overflow-y-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-200\",\n                                                            children: [\n                                                                \"Store Name \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-400\",\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 600,\n                                                                    columnNumber: 34\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 599,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            ref: storeNameInputRef,\n                                                            type: \"text\",\n                                                            value: storeForm.name,\n                                                            onChange: (e)=>{\n                                                                setStoreForm({\n                                                                    ...storeForm,\n                                                                    name: e.target.value\n                                                                });\n                                                                if (formErrors.name) setFormErrors({\n                                                                    ...formErrors,\n                                                                    name: undefined\n                                                                });\n                                                            },\n                                                            className: \"w-full px-4 py-3 bg-slate-800/60 border rounded-xl text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-2 transition-all \".concat(formErrors.name ? \"border-red-500/50 focus:ring-red-400/50 focus:border-red-400/50\" : \"border-slate-600/50 focus:ring-emerald-400/50 focus:border-emerald-400/50\"),\n                                                            placeholder: \"e.g., Tech Haven, Fashion Forward, Green Groceries\",\n                                                            maxLength: 50\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        formErrors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-400 text-xs flex items-center mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3 h-3 mr-1\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                        lineNumber: 621,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 620,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                formErrors.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-slate-400\",\n                                                            children: [\n                                                                storeForm.name.length,\n                                                                \"/50 characters\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-200\",\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: storeForm.description,\n                                                            onChange: (e)=>{\n                                                                setStoreForm({\n                                                                    ...storeForm,\n                                                                    description: e.target.value\n                                                                });\n                                                                if (formErrors.description) setFormErrors({\n                                                                    ...formErrors,\n                                                                    description: undefined\n                                                                });\n                                                            },\n                                                            className: \"w-full px-4 py-3 bg-slate-800/60 border rounded-xl text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-2 transition-all resize-none \".concat(formErrors.description ? \"border-red-500/50 focus:ring-red-400/50 focus:border-red-400/50\" : \"border-slate-600/50 focus:ring-emerald-400/50 focus:border-emerald-400/50\"),\n                                                            placeholder: \"Describe your store's mission, products, or specialty...\",\n                                                            rows: 3,\n                                                            maxLength: 200\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        formErrors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-400 text-xs flex items-center mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3 h-3 mr-1\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                        lineNumber: 650,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 649,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                formErrors.description\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 648,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-slate-400\",\n                                                            children: [\n                                                                storeForm.description.length,\n                                                                \"/200 characters\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                    lineNumber: 630,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-slate-200\",\n                                                                    children: [\n                                                                        \"Currency \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-red-400\",\n                                                                            children: \"*\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                            lineNumber: 663,\n                                                                            columnNumber: 34\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 662,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: storeForm.currency,\n                                                                    onChange: (e)=>{\n                                                                        setStoreForm({\n                                                                            ...storeForm,\n                                                                            currency: e.target.value\n                                                                        });\n                                                                        if (formErrors.currency) setFormErrors({\n                                                                            ...formErrors,\n                                                                            currency: undefined\n                                                                        });\n                                                                    },\n                                                                    className: \"w-full px-4 py-3 bg-slate-800/60 border rounded-xl text-slate-100 focus:outline-none focus:ring-2 transition-all \".concat(formErrors.currency ? \"border-red-500/50 focus:ring-red-400/50 focus:border-red-400/50\" : \"border-slate-600/50 focus:ring-emerald-400/50 focus:border-emerald-400/50\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"Select currency\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                            lineNumber: 677,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        currencyOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: option.value,\n                                                                                children: [\n                                                                                    option.symbol,\n                                                                                    \" \",\n                                                                                    option.label\n                                                                                ]\n                                                                            }, option.value, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                lineNumber: 679,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                formErrors.currency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-red-400 text-xs flex items-center mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-3 h-3 mr-1\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                lineNumber: 687,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                            lineNumber: 686,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        formErrors.currency\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 685,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-slate-200\",\n                                                                    children: [\n                                                                        \"Language \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-red-400\",\n                                                                            children: \"*\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                            lineNumber: 697,\n                                                                            columnNumber: 34\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 696,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: storeForm.preferredLanguage,\n                                                                    onChange: (e)=>{\n                                                                        setStoreForm({\n                                                                            ...storeForm,\n                                                                            preferredLanguage: e.target.value\n                                                                        });\n                                                                        if (formErrors.preferredLanguage) setFormErrors({\n                                                                            ...formErrors,\n                                                                            preferredLanguage: undefined\n                                                                        });\n                                                                    },\n                                                                    className: \"w-full px-4 py-3 bg-slate-800/60 border rounded-xl text-slate-100 focus:outline-none focus:ring-2 transition-all \".concat(formErrors.preferredLanguage ? \"border-red-500/50 focus:ring-red-400/50 focus:border-red-400/50\" : \"border-slate-600/50 focus:ring-emerald-400/50 focus:border-emerald-400/50\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"Select language\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                            lineNumber: 711,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        languageOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: option.value,\n                                                                                children: option.nativeLabel\n                                                                            }, option.value, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                lineNumber: 713,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 699,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                formErrors.preferredLanguage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-red-400 text-xs flex items-center mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-3 h-3 mr-1\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                                lineNumber: 721,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                            lineNumber: 720,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        formErrors.preferredLanguage\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 719,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 695,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                    lineNumber: 659,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 border-t border-white/10 flex items-center justify-end gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsStoreModalOpen(false),\n                                                disabled: isSubmitting,\n                                                className: \"border border-slate-600 text-slate-300 bg-slate-800/50 hover:bg-slate-700/50 px-4 py-2 rounded-lg transition-colors disabled:opacity-50\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleSubmitStore,\n                                                disabled: isSubmitting || !storeForm.name.trim() || !storeForm.currency || !storeForm.preferredLanguage,\n                                                className: \"px-6 py-2 bg-gradient-to-r from-emerald-600 to-emerald-500 hover:from-emerald-500 hover:to-emerald-400 text-white rounded-lg font-medium shadow-lg shadow-emerald-500/25 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center\",\n                                                children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    className: \"opacity-25\",\n                                                                    cx: \"12\",\n                                                                    cy: \"12\",\n                                                                    r: \"10\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 748,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    className: \"opacity-75\",\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 749,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 747,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        modalMode === \"create\" ? \"Creating...\" : \"Updating...\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: modalMode === \"create\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 mr-1\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 758,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                lineNumber: 757,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Create Store\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 mr-1\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 765,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                lineNumber: 764,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Update Store\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 740,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                        lineNumber: 732,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                lineNumber: 575,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                        lineNumber: 571,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                lineNumber: 567,\n                columnNumber: 9\n            }, this),\n            isDeleteOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/50\",\n                        onClick: ()=>setIsDeleteOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                        lineNumber: 782,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full max-w-md overflow-hidden rounded-2xl border border-white/10 shadow-2xl bg-gradient-to-br from-slate-800/95 to-slate-900/95\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -inset-0.5 rounded-2xl bg-gradient-to-r from-red-500/20 via-orange-500/20 to-red-500/20 blur opacity-60\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                lineNumber: 786,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 border-b border-white/10 flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full bg-red-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 793,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-slate-100\",\n                                                        children: \"Delete Store\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 794,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 792,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsDeleteOpen(false),\n                                                disabled: isSubmitting,\n                                                className: \"text-slate-400 hover:text-slate-200 rounded-md p-1 transition-colors disabled:opacity-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 802,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                    lineNumber: 801,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 796,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                        lineNumber: 791,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-red-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 812,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                        lineNumber: 811,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                    lineNumber: 810,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-slate-100 font-medium mb-2\",\n                                                            children: \"Are you absolutely sure?\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 816,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-300 mb-3\",\n                                                            children: [\n                                                                \"This will permanently delete the store \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-slate-100\",\n                                                                    children: [\n                                                                        '\"',\n                                                                        (_stores_find = stores.find((s)=>s.id === deleteStoreId)) === null || _stores_find === void 0 ? void 0 : _stores_find.name,\n                                                                        '\"'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 818,\n                                                                    columnNumber: 62\n                                                                }, this),\n                                                                \" and all associated data.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 817,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-red-400\",\n                                                            children: \"This action cannot be undone.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 820,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                    lineNumber: 815,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                            lineNumber: 809,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                        lineNumber: 808,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 border-t border-white/10 flex items-center justify-end gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsDeleteOpen(false),\n                                                disabled: isSubmitting,\n                                                className: \"border border-slate-600 text-slate-300 bg-slate-800/50 hover:bg-slate-700/50 px-4 py-2 rounded-lg transition-colors disabled:opacity-50\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 829,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleDeleteStore,\n                                                disabled: isSubmitting,\n                                                className: \"px-4 py-2 bg-gradient-to-r from-red-600 to-red-500 hover:from-red-500 hover:to-red-400 text-white rounded-lg font-medium shadow-lg shadow-red-500/25 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center\",\n                                                children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    className: \"opacity-25\",\n                                                                    cx: \"12\",\n                                                                    cy: \"12\",\n                                                                    r: \"10\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 844,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    className: \"opacity-75\",\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                    lineNumber: 845,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 843,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Deleting...\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 mr-1\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                                lineNumber: 852,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                            lineNumber: 851,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Delete Store\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                                lineNumber: 836,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                        lineNumber: 828,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                                lineNumber: 789,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                        lineNumber: 785,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n                lineNumber: 781,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\TopTaskBar.tsx\",\n        lineNumber: 362,\n        columnNumber: 5\n    }, this);\n}\n_s(TopTaskBar, \"zO63j/sXuPiVyxu0QjWBcSXjuGM=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _context_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _context_PreferencesContext__WEBPACK_IMPORTED_MODULE_6__.usePreferences,\n        _context_StoreContext__WEBPACK_IMPORTED_MODULE_5__.useStore\n    ];\n});\n_c = TopTaskBar;\nvar _c;\n$RefreshReg$(_c, \"TopTaskBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/TopTaskBar.tsx\n"));

/***/ }),

/***/ "./src/pages/dashboard.tsx":
/*!*********************************!*\
  !*** ./src/pages/dashboard.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Dashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _context_StoreContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../context/StoreContext */ \"./src/context/StoreContext.tsx\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/api */ \"./src/utils/api.ts\");\n/* harmony import */ var _components_TopTaskBar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/TopTaskBar */ \"./src/components/TopTaskBar.tsx\");\n/* harmony import */ var _components_SideTaskBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/SideTaskBar */ \"./src/components/SideTaskBar.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Dashboard() {\n    var _storesData_data;\n    _s();\n    const { user, isLoading, error, refresh, logout } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { autoSelectFirstStore } = (0,_context_StoreContext__WEBPACK_IMPORTED_MODULE_5__.useStore)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [storesData, setStoresData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [storesLoading, setStoresLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasCheckedStores, setHasCheckedStores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check for auth success\n        if (router.query.auth === \"success\") {\n            // Remove the query param from URL\n            router.replace(\"/dashboard\", undefined, {\n                shallow: true\n            });\n        }\n        // Fetch user data\n        refresh();\n    }, [\n        router.query.auth,\n        refresh\n    ]);\n    // Fetch stores when user is available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user === null || user === void 0 ? void 0 : user.id) {\n            console.log(\"Fetching stores for user:\", user.id);\n            setStoresLoading(true);\n            _utils_api__WEBPACK_IMPORTED_MODULE_6__.storeApi.getByUserId(user.id, {\n                page: 1,\n                limit: 100\n            }).then((data)=>{\n                var _data_data;\n                console.log(\"Stores data received:\", data);\n                setStoresData(data);\n                // Auto-select the first store if available\n                if ((data === null || data === void 0 ? void 0 : (_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.length) > 0) {\n                    autoSelectFirstStore(data.data);\n                }\n                setHasCheckedStores(true);\n            }).catch((error)=>{\n                console.error(\"Failed to fetch stores:\", error);\n                setHasCheckedStores(true);\n            }).finally(()=>{\n                setStoresLoading(false);\n            });\n        }\n    }, [\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    // Fallback: if authenticated here and have zero stores, send to setup\n    // Only redirect if we've actually checked for stores and found none\n    // AND we're not coming from store creation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading && user && !storesLoading && hasCheckedStores) {\n            var _storesData_meta, _storesData_data;\n            var _storesData_meta_total, _ref;\n            const totalStores = (_ref = (_storesData_meta_total = storesData === null || storesData === void 0 ? void 0 : (_storesData_meta = storesData.meta) === null || _storesData_meta === void 0 ? void 0 : _storesData_meta.total) !== null && _storesData_meta_total !== void 0 ? _storesData_meta_total : storesData === null || storesData === void 0 ? void 0 : (_storesData_data = storesData.data) === null || _storesData_data === void 0 ? void 0 : _storesData_data.length) !== null && _ref !== void 0 ? _ref : 0;\n            const isFromStoreCreation = router.query.from === \"store-creation\";\n            const hasStoreCreationFlag = (()=>{\n                try {\n                    return localStorage.getItem(\"teno:store-creation-complete\") === \"true\";\n                } catch (e) {\n                    return false;\n                }\n            })();\n            console.log(\"Dashboard redirect check:\", {\n                totalStores,\n                isFromStoreCreation,\n                hasStoreCreationFlag,\n                storesData,\n                hasCheckedStores\n            });\n            // Only redirect to setup if user has no active stores\n            if (totalStores === 0 && !isFromStoreCreation && !hasStoreCreationFlag) {\n                console.log(\"Redirecting to setup store - no active stores found\");\n                router.replace(\"/setup/store\");\n            } else if (totalStores > 0) {\n                console.log(\"Active stores found, staying on dashboard\");\n                // Clear the store creation flag since we have stores now\n                try {\n                    localStorage.removeItem(\"teno:store-creation-complete\");\n                } catch (e) {}\n            } else if (isFromStoreCreation || hasStoreCreationFlag) {\n                console.log(\"Coming from store creation, staying on dashboard\");\n                // Clear the store creation flag\n                try {\n                    localStorage.removeItem(\"teno:store-creation-complete\");\n                } catch (e) {}\n            }\n        }\n    }, [\n        isLoading,\n        user,\n        storesLoading,\n        storesData,\n        router,\n        hasCheckedStores\n    ]);\n    // Clean up the 'from' query parameter after processing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (router.query.from === \"store-creation\") {\n            // Remove the query param from URL to keep it clean\n            router.replace(\"/dashboard\", undefined, {\n                shallow: true\n            });\n        }\n    }, [\n        router.query.from,\n        router\n    ]);\n    // Clean up localStorage flag when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            try {\n                localStorage.removeItem(\"teno:store-creation-complete\");\n            } catch (e) {}\n        };\n    }, [\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    const handleLogout = async ()=>{\n        await logout();\n        router.push(\"/\");\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                lineNumber: 124,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n            lineNumber: 123,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                        children: \"Authentication Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleLogout,\n                        className: \"bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700\",\n                        children: \"Logout\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        router.push(\"/login\");\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Dashboard - Teno Store\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Manage your stores and business\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TopTaskBar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SideTaskBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"flex-1 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold text-gray-900\",\n                                                    children: \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mt-2\",\n                                                    children: [\n                                                        \"Welcome back, \",\n                                                        user.name,\n                                                        \"!\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this),\n                                        storesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center py-12\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this) : (storesData === null || storesData === void 0 ? void 0 : (_storesData_data = storesData.data) === null || _storesData_data === void 0 ? void 0 : _storesData_data.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                            children: storesData.data.map((store)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer\",\n                                                    onClick: ()=>router.push(\"/store/\".concat(store.id)),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                                            children: store.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-4\",\n                                                            children: store.description || \"No description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Currency: \",\n                                                                        store.currency\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                                    lineNumber: 186,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Language: \",\n                                                                        store.preferredLanguage\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, store.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: \"No stores found. Let's create your first store!\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>router.push(\"/setup/store\"),\n                                                    className: \"bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors\",\n                                                    children: \"Create Store\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Dashboard, \"/dJ/hPEvsrXFHKorOTxSk3Rj64s=\", false, function() {\n    return [\n        _context_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _context_StoreContext__WEBPACK_IMPORTED_MODULE_5__.useStore,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/dashboard.tsx\n"));

/***/ }),

/***/ "./src/utils/api.ts":
/*!**************************!*\
  !*** ./src/utils/api.ts ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: function() { return /* binding */ ApiClient; },\n/* harmony export */   agentApi: function() { return /* binding */ agentApi; },\n/* harmony export */   apiClient: function() { return /* binding */ apiClient; },\n/* harmony export */   conversationApi: function() { return /* binding */ conversationApi; },\n/* harmony export */   customerApi: function() { return /* binding */ customerApi; },\n/* harmony export */   imageApi: function() { return /* binding */ imageApi; },\n/* harmony export */   orderApi: function() { return /* binding */ orderApi; },\n/* harmony export */   productApi: function() { return /* binding */ productApi; },\n/* harmony export */   storeApi: function() { return /* binding */ storeApi; },\n/* harmony export */   userApi: function() { return /* binding */ userApi; }\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth */ \"./src/utils/auth.ts\");\n\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\n// Helper function to handle API responses\nasync function handleResponse(response) {\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new Error(errorData.error || \"HTTP error! status: \".concat(response.status));\n    }\n    return response.json();\n}\n// Generic API client class\nclass ApiClient {\n    // Generic GET request\n    async get(endpoint, params) {\n        const url = new URL(\"\".concat(this.baseUrl).concat(endpoint));\n        if (params) {\n            Object.entries(params).forEach((param)=>{\n                let [key, value] = param;\n                if (value !== undefined && value !== null) {\n                    url.searchParams.append(key, String(value));\n                }\n            });\n        }\n        console.log(\"API GET request to:\", url.toString());\n        const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.fetchWithCredentials)(url.toString(), {\n            method: \"GET\"\n        });\n        return handleResponse(response);\n    }\n    // Generic POST request\n    async post(endpoint, data) {\n        const url = \"\".concat(this.baseUrl).concat(endpoint);\n        console.log(\"\\uD83D\\uDD27 API POST request to:\", url);\n        console.log(\"\\uD83D\\uDD27 API POST data:\", data);\n        console.log(\"\\uD83D\\uDD27 API base URL:\", this.baseUrl);\n        try {\n            const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.fetchWithCredentials)(url, {\n                method: \"POST\",\n                body: data ? JSON.stringify(data) : undefined\n            });\n            console.log(\"\\uD83D\\uDD27 API POST response status:\", response.status);\n            console.log(\"\\uD83D\\uDD27 API POST response headers:\", Object.fromEntries(response.headers.entries()));\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"❌ API POST error response:\", errorText);\n                throw new Error(\"HTTP error! status: \".concat(response.status, \", body: \").concat(errorText));\n            }\n            return handleResponse(response);\n        } catch (error) {\n            console.error(\"❌ API POST fetch error:\", error);\n            throw error;\n        }\n    }\n    // Generic PUT request\n    async put(endpoint, data) {\n        const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.fetchWithCredentials)(\"\".concat(this.baseUrl).concat(endpoint), {\n            method: \"PUT\",\n            body: data ? JSON.stringify(data) : undefined\n        });\n        return handleResponse(response);\n    }\n    // Generic DELETE request\n    async delete(endpoint) {\n        const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.fetchWithCredentials)(\"\".concat(this.baseUrl).concat(endpoint), {\n            method: \"DELETE\"\n        });\n        return handleResponse(response);\n    }\n    constructor(baseUrl = API_BASE_URL){\n        this.baseUrl = baseUrl;\n    }\n}\n// Create a default API client instance\nconst apiClient = new ApiClient();\n// Store API methods\nconst storeApi = {\n    getAll: (params)=>apiClient.get(\"/stores\", params),\n    getById: (id)=>apiClient.get(\"/stores/\".concat(id)),\n    getByUserId: (userId, params)=>apiClient.get(\"/stores/user/\".concat(userId), params),\n    getByUuid: (uuid)=>apiClient.get(\"/stores/uuid/\".concat(uuid)),\n    create: (data)=>apiClient.post(\"/stores\", data),\n    update: (id, data)=>apiClient.put(\"/stores/\".concat(id), data),\n    delete: (id)=>apiClient.delete(\"/stores/\".concat(id))\n};\n// Product API methods\nconst productApi = {\n    getAll: (params)=>apiClient.get(\"/products\", params),\n    getById: (id)=>apiClient.get(\"/products/\".concat(id)),\n    getByStoreId: (storeId, params)=>apiClient.get(\"/products/store/\".concat(storeId), params),\n    getByStoreUuid: (storeUuid, params)=>apiClient.get(\"/products/store/uuid/\".concat(storeUuid), params),\n    create: (data)=>apiClient.post(\"/products\", data),\n    update: (id, data)=>apiClient.put(\"/products/\".concat(id), data),\n    delete: (id)=>apiClient.delete(\"/products/\".concat(id))\n};\n// Customer API methods\nconst customerApi = {\n    getAll: (params)=>apiClient.get(\"/customers\", params),\n    getById: (id)=>apiClient.get(\"/customers/\".concat(id)),\n    getByStoreId: (storeId, params)=>apiClient.get(\"/customers/store/\".concat(storeId), params),\n    create: (data)=>apiClient.post(\"/customers\", data),\n    update: (id, data)=>apiClient.put(\"/customers/\".concat(id), data),\n    delete: (id)=>apiClient.delete(\"/customers/\".concat(id))\n};\n// User API methods\nconst userApi = {\n    getAll: (params)=>apiClient.get(\"/users\", params),\n    getById: (id)=>apiClient.get(\"/users/\".concat(id)),\n    create: (data)=>apiClient.post(\"/users\", data),\n    update: (id, data)=>apiClient.put(\"/users/\".concat(id), data),\n    delete: (id)=>apiClient.delete(\"/users/\".concat(id))\n};\n// Order API methods\nconst orderApi = {\n    getAll: (params)=>apiClient.get(\"/orders\", params),\n    getById: (id)=>apiClient.get(\"/orders/\".concat(id)),\n    getByOrderNumber: (orderNumber)=>apiClient.get(\"/orders/order-number/\".concat(orderNumber)),\n    getByStoreId: (storeId, params)=>apiClient.get(\"/orders/store/\".concat(storeId), params),\n    filter: (data)=>apiClient.post(\"/orders/filter\", data),\n    create: (data)=>apiClient.post(\"/orders\", data),\n    update: (id, data)=>apiClient.put(\"/orders/\".concat(id), data),\n    delete: (id)=>apiClient.delete(\"/orders/\".concat(id))\n};\n// Conversation API methods (using existing endpoints)\nconst conversationApi = {\n    getAll: (params)=>apiClient.get(\"/conversations\", params),\n    getById: (id)=>apiClient.get(\"/conversations/\".concat(id)),\n    getByUuid: (uuid)=>apiClient.get(\"/conversations/uuid/\".concat(uuid)),\n    getByStoreId: (storeId, params)=>apiClient.get(\"/conversations/store/\".concat(storeId), params),\n    getByUserId: (userId, params)=>apiClient.get(\"/conversations/user/\".concat(userId), params),\n    create: (data)=>apiClient.post(\"/conversations\", data),\n    getTimeline: (id, params)=>apiClient.get(\"/conversations/\".concat(id, \"/timeline\"), params),\n    getUnifiedTimeline: (id, params)=>apiClient.get(\"/conversations/\".concat(id, \"/unified-timeline\"), params),\n    getUnifiedTimelineByUuid: (uuid, params)=>apiClient.get(\"/conversations/uuid/\".concat(uuid, \"/unified-timeline\"), params),\n    appendMessage: (id, data)=>apiClient.post(\"/conversations/\".concat(id, \"/messages\"), data),\n    appendMessageByUuid: (uuid, data)=>apiClient.post(\"/conversations/uuid/\".concat(uuid, \"/messages\"), data)\n};\n// Image API methods\nconst imageApi = {\n    upload: (file)=>{\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        return (0,_auth__WEBPACK_IMPORTED_MODULE_0__.fetchWithCredentials)(\"/images\", {\n            method: \"POST\",\n            body: formData\n        }).then((response)=>{\n            if (!response.ok) {\n                return response.json().then((errorData)=>{\n                    throw new Error(errorData.error || \"Failed to upload image\");\n                });\n            }\n            return response.json();\n        });\n    }\n};\n// Agent API methods (using existing endpoints)\nconst agentApi = {\n    getAll: (params)=>apiClient.get(\"/agents\", params),\n    getById: (id)=>apiClient.get(\"/agents/\".concat(id)),\n    create: (data)=>apiClient.post(\"/agents\", data),\n    update: (id, data)=>apiClient.put(\"/agents/\".concat(id), data),\n    delete: (id)=>apiClient.delete(\"/agents/\".concat(id)),\n    generateMessage: (data)=>apiClient.post(\"/agents/runtime/generate-message\", data)\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/api.ts\n"));

/***/ }),

/***/ "./node_modules/next/head.js":
/*!***********************************!*\
  !*** ./node_modules/next/head.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/shared/lib/head */ \"./node_modules/next/dist/shared/lib/head.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzIiwibWFwcGluZ3MiOiJBQUFBLGlIQUFrRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzPzg4NDkiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3Qvc2hhcmVkL2xpYi9oZWFkJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/head.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cteno-store%5Cfrontend%5Csrc%5Cpages%5Cdashboard.tsx&page=%2Fdashboard!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);